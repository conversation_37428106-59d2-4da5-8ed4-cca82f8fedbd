2025/07/06-15:54:52.767243 6095417344 RocksDB version: 8.1.1
2025/07/06-15:54:52.768142 6099709952 Compile date 2023-04-06 16:38:52
2025/07/06-15:54:52.768145 6099709952 DB SUMMARY
2025/07/06-15:54:52.768146 6099709952 DB Session ID:  T4DI5BBINSFBXHGOANDG
2025/07/06-15:54:52.768170 6099709952 SST files in test_data dir, Total Num: 0, files: 
2025/07/06-15:54:52.768173 6099709952 Write Ahead Log file in test_data: 
2025/07/06-15:54:52.768177 6099709952                         Options.error_if_exists: 0
2025/07/06-15:54:52.768178 6099709952                       Options.create_if_missing: 1
2025/07/06-15:54:52.768186 6099709952                         Options.paranoid_checks: 1
2025/07/06-15:54:52.768189 6099709952             Options.flush_verify_memtable_count: 1
2025/07/06-15:54:52.768196 6099709952                               Options.track_and_verify_wals_in_manifest: 0
2025/07/06-15:54:52.768201 6099709952        Options.verify_sst_unique_id_in_manifest: 1
2025/07/06-15:54:52.768206 6099709952                                     Options.env: 0x1058f7820
2025/07/06-15:54:52.768210 6099709952                                      Options.fs: PosixFileSystem
2025/07/06-15:54:52.768218 6099709952                                Options.info_log: 0x146504a48
2025/07/06-15:54:52.768224 6099709952                Options.max_file_opening_threads: 16
2025/07/06-15:54:52.768229 6099709952                              Options.statistics: 0x0
2025/07/06-15:54:52.768230 6099709952                               Options.use_fsync: 0
2025/07/06-15:54:52.768234 6099709952                       Options.max_log_file_size: 0
2025/07/06-15:54:52.768239 6099709952                  Options.max_manifest_file_size: 1073741824
2025/07/06-15:54:52.768242 6099709952                   Options.log_file_time_to_roll: 0
2025/07/06-15:54:52.768244 6099709952                       Options.keep_log_file_num: 1000
2025/07/06-15:54:52.768246 6099709952                    Options.recycle_log_file_num: 0
2025/07/06-15:54:52.768251 6099709952                         Options.allow_fallocate: 1
2025/07/06-15:54:52.768253 6099709952                        Options.allow_mmap_reads: 0
2025/07/06-15:54:52.768255 6099709952                       Options.allow_mmap_writes: 0
2025/07/06-15:54:52.768269 6099709952                        Options.use_direct_reads: 0
2025/07/06-15:54:52.768276 6099709952                        Options.use_direct_io_for_flush_and_compaction: 0
2025/07/06-15:54:52.768290 6099709952          Options.create_missing_column_families: 1
2025/07/06-15:54:52.768292 6099709952                              Options.db_log_dir: 
2025/07/06-15:54:52.768293 6099709952                                 Options.wal_dir: 
2025/07/06-15:54:52.768299 6099709952                Options.table_cache_numshardbits: 6
2025/07/06-15:54:52.768309 6099709952                         Options.WAL_ttl_seconds: 0
2025/07/06-15:54:52.768325 6099709952                       Options.WAL_size_limit_MB: 0
2025/07/06-15:54:52.768329 6099709952                        Options.max_write_batch_group_size_bytes: 1048576
2025/07/06-15:54:52.768336 6099709952             Options.manifest_preallocation_size: 4194304
2025/07/06-15:54:52.768346 6099709952                     Options.is_fd_close_on_exec: 1
2025/07/06-15:54:52.768356 6099709952                   Options.advise_random_on_open: 1
2025/07/06-15:54:52.768359 6099709952                    Options.db_write_buffer_size: 0
2025/07/06-15:54:52.768364 6099709952                    Options.write_buffer_manager: 0x146504ba0
2025/07/06-15:54:52.768369 6099709952         Options.access_hint_on_compaction_start: 1
2025/07/06-15:54:52.768374 6099709952           Options.random_access_max_buffer_size: 1048576
2025/07/06-15:54:52.768383 6099709952                      Options.use_adaptive_mutex: 0
2025/07/06-15:54:52.768397 6099709952                            Options.rate_limiter: 0x0
2025/07/06-15:54:52.768402 6099709952     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/07/06-15:54:52.768406 6099709952                       Options.wal_recovery_mode: 2
2025/07/06-15:54:52.768411 6099709952                  Options.enable_thread_tracking: 0
2025/07/06-15:54:52.768418 6099709952                  Options.enable_pipelined_write: 0
2025/07/06-15:54:52.768428 6099709952                  Options.unordered_write: 0
2025/07/06-15:54:52.768429 6099709952         Options.allow_concurrent_memtable_write: 1
2025/07/06-15:54:52.768434 6099709952      Options.enable_write_thread_adaptive_yield: 1
2025/07/06-15:54:52.768440 6099709952             Options.write_thread_max_yield_usec: 100
2025/07/06-15:54:52.768442 6099709952            Options.write_thread_slow_yield_usec: 3
2025/07/06-15:54:52.768451 6099709952                               Options.row_cache: None
2025/07/06-15:54:52.768453 6099709952                              Options.wal_filter: None
2025/07/06-15:54:52.768454 6099709952             Options.avoid_flush_during_recovery: 0
2025/07/06-15:54:52.768466 6099709952             Options.allow_ingest_behind: 0
2025/07/06-15:54:52.768470 6099709952             Options.two_write_queues: 0
2025/07/06-15:54:52.768478 6099709952             Options.manual_wal_flush: 0
2025/07/06-15:54:52.768482 6099709952             Options.wal_compression: 0
2025/07/06-15:54:52.768490 6099709952             Options.atomic_flush: 0
2025/07/06-15:54:52.768496 6099709952             Options.avoid_unnecessary_blocking_io: 0
2025/07/06-15:54:52.768502 6099709952                 Options.persist_stats_to_disk: 0
2025/07/06-15:54:52.768504 6099709952                 Options.write_dbid_to_manifest: 0
2025/07/06-15:54:52.768510 6099709952                 Options.log_readahead_size: 0
2025/07/06-15:54:52.768514 6099709952                 Options.file_checksum_gen_factory: Unknown
2025/07/06-15:54:52.768515 6099709952                 Options.best_efforts_recovery: 0
2025/07/06-15:54:52.768517 6099709952                Options.max_bgerror_resume_count: 2147483647
2025/07/06-15:54:52.768519 6099709952            Options.bgerror_resume_retry_interval: 1000000
2025/07/06-15:54:52.768527 6099709952             Options.allow_data_in_errors: 0
2025/07/06-15:54:52.768528 6099709952             Options.db_host_id: __hostname__
2025/07/06-15:54:52.768535 6099709952             Options.enforce_single_del_contracts: true
2025/07/06-15:54:52.768539 6099709952             Options.max_background_jobs: 2
2025/07/06-15:54:52.768541 6099709952             Options.max_background_compactions: -1
2025/07/06-15:54:52.768547 6099709952             Options.max_subcompactions: 1
2025/07/06-15:54:52.768557 6099709952             Options.avoid_flush_during_shutdown: 0
2025/07/06-15:54:52.768559 6099709952           Options.writable_file_max_buffer_size: 1048576
2025/07/06-15:54:52.768567 6099709952             Options.delayed_write_rate : 16777216
2025/07/06-15:54:52.768574 6099709952             Options.max_total_wal_size: 0
2025/07/06-15:54:52.768575 6099709952             Options.delete_obsolete_files_period_micros: 21600000000
2025/07/06-15:54:52.768584 6099709952                   Options.stats_dump_period_sec: 600
2025/07/06-15:54:52.768591 6099709952                 Options.stats_persist_period_sec: 600
2025/07/06-15:54:52.768593 6099709952                 Options.stats_history_buffer_size: 1048576
2025/07/06-15:54:52.768596 6099709952                          Options.max_open_files: -1
2025/07/06-15:54:52.768597 6099709952                          Options.bytes_per_sync: 0
2025/07/06-15:54:52.768600 6099709952                      Options.wal_bytes_per_sync: 0
2025/07/06-15:54:52.768601 6099709952                   Options.strict_bytes_per_sync: 0
2025/07/06-15:54:52.768606 6099709952       Options.compaction_readahead_size: 0
2025/07/06-15:54:52.768614 6099709952                  Options.max_background_flushes: -1
2025/07/06-15:54:52.768621 6099709952 Compression algorithms supported:
2025/07/06-15:54:52.768631 6099709952 	kZSTD supported: 1
2025/07/06-15:54:52.768637 6099709952 	kZlibCompression supported: 1
2025/07/06-15:54:52.768642 6099709952 	kXpressCompression supported: 0
2025/07/06-15:54:52.768643 6099709952 	kSnappyCompression supported: 1
2025/07/06-15:54:52.768647 6099709952 	kZSTDNotFinalCompression supported: 1
2025/07/06-15:54:52.768654 6099709952 	kLZ4HCCompression supported: 1
2025/07/06-15:54:52.768659 6099709952 	kLZ4Compression supported: 1
2025/07/06-15:54:52.768662 6099709952 	kBZip2Compression supported: 1
2025/07/06-15:54:52.768669 6099709952 Fast CRC32 supported: Supported on Arm64
2025/07/06-15:54:52.768674 6099709952 DMutex implementation: pthread_mutex_t
2025/07/06-15:54:52.769071 6099709952 [db/db_impl/db_impl_open.cc:315] Creating manifest 1 
2025/07/06-15:54:52.769728 6099709952 [db/version_set.cc:5662] Recovering from manifest file: test_data/MANIFEST-000001
2025/07/06-15:54:52.770069 6099709952 [db/column_family.cc:621] --------------- Options for column family [default]:
2025/07/06-15:54:52.770071 6099709952               Options.comparator: leveldb.BytewiseComparator
2025/07/06-15:54:52.770073 6099709952           Options.merge_operator: None
2025/07/06-15:54:52.770075 6099709952        Options.compaction_filter: None
2025/07/06-15:54:52.770076 6099709952        Options.compaction_filter_factory: None
2025/07/06-15:54:52.770078 6099709952  Options.sst_partitioner_factory: None
2025/07/06-15:54:52.770080 6099709952         Options.memtable_factory: SkipListFactory
2025/07/06-15:54:52.770082 6099709952            Options.table_factory: BlockBasedTable
2025/07/06-15:54:52.770107 6099709952            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x144637220)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x144637278
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: 0x0
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/06-15:54:52.770114 6099709952        Options.write_buffer_size: 67108864
2025/07/06-15:54:52.770116 6099709952  Options.max_write_buffer_number: 2
2025/07/06-15:54:52.770118 6099709952          Options.compression: Snappy
2025/07/06-15:54:52.770120 6099709952                  Options.bottommost_compression: Disabled
2025/07/06-15:54:52.770122 6099709952       Options.prefix_extractor: nullptr
2025/07/06-15:54:52.770124 6099709952   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/06-15:54:52.770126 6099709952             Options.num_levels: 7
2025/07/06-15:54:52.770128 6099709952        Options.min_write_buffer_number_to_merge: 1
2025/07/06-15:54:52.770130 6099709952     Options.max_write_buffer_number_to_maintain: 0
2025/07/06-15:54:52.770131 6099709952     Options.max_write_buffer_size_to_maintain: 0
2025/07/06-15:54:52.770134 6099709952            Options.bottommost_compression_opts.window_bits: -14
2025/07/06-15:54:52.770136 6099709952                  Options.bottommost_compression_opts.level: 32767
2025/07/06-15:54:52.770137 6099709952               Options.bottommost_compression_opts.strategy: 0
2025/07/06-15:54:52.770139 6099709952         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/06-15:54:52.770140 6099709952         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/06-15:54:52.770145 6099709952         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/06-15:54:52.770148 6099709952                  Options.bottommost_compression_opts.enabled: false
2025/07/06-15:54:52.770149 6099709952         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/06-15:54:52.770151 6099709952         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/06-15:54:52.770153 6099709952            Options.compression_opts.window_bits: -14
2025/07/06-15:54:52.770156 6099709952                  Options.compression_opts.level: 32767
2025/07/06-15:54:52.770158 6099709952               Options.compression_opts.strategy: 0
2025/07/06-15:54:52.770160 6099709952         Options.compression_opts.max_dict_bytes: 0
2025/07/06-15:54:52.770162 6099709952         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/06-15:54:52.770164 6099709952         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/06-15:54:52.770165 6099709952         Options.compression_opts.parallel_threads: 1
2025/07/06-15:54:52.770168 6099709952                  Options.compression_opts.enabled: false
2025/07/06-15:54:52.770170 6099709952         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/06-15:54:52.770172 6099709952      Options.level0_file_num_compaction_trigger: 4
2025/07/06-15:54:52.770177 6099709952          Options.level0_slowdown_writes_trigger: 20
2025/07/06-15:54:52.770179 6099709952              Options.level0_stop_writes_trigger: 36
2025/07/06-15:54:52.770180 6099709952                   Options.target_file_size_base: 67108864
2025/07/06-15:54:52.770185 6099709952             Options.target_file_size_multiplier: 1
2025/07/06-15:54:52.770187 6099709952                Options.max_bytes_for_level_base: 268435456
2025/07/06-15:54:52.770189 6099709952 Options.level_compaction_dynamic_level_bytes: 0
2025/07/06-15:54:52.770191 6099709952          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/06-15:54:52.770193 6099709952 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/06-15:54:52.770196 6099709952 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/06-15:54:52.770197 6099709952 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/06-15:54:52.770200 6099709952 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/06-15:54:52.770202 6099709952 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/06-15:54:52.770204 6099709952 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/06-15:54:52.770208 6099709952 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/06-15:54:52.770211 6099709952       Options.max_sequential_skip_in_iterations: 8
2025/07/06-15:54:52.770213 6099709952                    Options.max_compaction_bytes: 1677721600
2025/07/06-15:54:52.770215 6099709952   Options.ignore_max_compaction_bytes_for_input: true
2025/07/06-15:54:52.770218 6099709952                        Options.arena_block_size: 1048576
2025/07/06-15:54:52.770220 6099709952   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/06-15:54:52.770222 6099709952   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/06-15:54:52.770225 6099709952                Options.disable_auto_compactions: 0
2025/07/06-15:54:52.770227 6099709952                        Options.compaction_style: kCompactionStyleLevel
2025/07/06-15:54:52.770230 6099709952                          Options.compaction_pri: kMinOverlappingRatio
2025/07/06-15:54:52.770234 6099709952 Options.compaction_options_universal.size_ratio: 1
2025/07/06-15:54:52.770236 6099709952 Options.compaction_options_universal.min_merge_width: 2
2025/07/06-15:54:52.770237 6099709952 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/06-15:54:52.770240 6099709952 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/06-15:54:52.770242 6099709952 Options.compaction_options_universal.compression_size_percent: -1
2025/07/06-15:54:52.770245 6099709952 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/06-15:54:52.770246 6099709952 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/06-15:54:52.770251 6099709952 Options.compaction_options_fifo.allow_compaction: 0
2025/07/06-15:54:52.770255 6099709952                   Options.table_properties_collectors: 
2025/07/06-15:54:52.770257 6099709952                   Options.inplace_update_support: 0
2025/07/06-15:54:52.770261 6099709952                 Options.inplace_update_num_locks: 10000
2025/07/06-15:54:52.770263 6099709952               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/06-15:54:52.770273 6099709952               Options.memtable_whole_key_filtering: 0
2025/07/06-15:54:52.770275 6099709952   Options.memtable_huge_page_size: 0
2025/07/06-15:54:52.770280 6099709952                           Options.bloom_locality: 0
2025/07/06-15:54:52.770283 6099709952                    Options.max_successive_merges: 0
2025/07/06-15:54:52.770285 6099709952                Options.optimize_filters_for_hits: 0
2025/07/06-15:54:52.770293 6099709952                Options.paranoid_file_checks: 0
2025/07/06-15:54:52.770294 6099709952                Options.force_consistency_checks: 1
2025/07/06-15:54:52.770299 6099709952                Options.report_bg_io_stats: 0
2025/07/06-15:54:52.770300 6099709952                               Options.ttl: 2592000
2025/07/06-15:54:52.770307 6099709952          Options.periodic_compaction_seconds: 0
2025/07/06-15:54:52.770312 6099709952  Options.preclude_last_level_data_seconds: 0
2025/07/06-15:54:52.770314 6099709952    Options.preserve_internal_time_seconds: 0
2025/07/06-15:54:52.770318 6099709952                       Options.enable_blob_files: false
2025/07/06-15:54:52.770325 6099709952                           Options.min_blob_size: 0
2025/07/06-15:54:52.770327 6099709952                          Options.blob_file_size: 268435456
2025/07/06-15:54:52.770332 6099709952                   Options.blob_compression_type: NoCompression
2025/07/06-15:54:52.770335 6099709952          Options.enable_blob_garbage_collection: false
2025/07/06-15:54:52.770337 6099709952      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/06-15:54:52.770338 6099709952 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/06-15:54:52.770340 6099709952          Options.blob_compaction_readahead_size: 0
2025/07/06-15:54:52.770342 6099709952                Options.blob_file_starting_level: 0
2025/07/06-15:54:52.770346 6099709952 Options.experimental_mempurge_threshold: 0.000000
2025/07/06-15:54:52.771712 6099709952 [db/version_set.cc:5713] Recovered from manifest file:test_data/MANIFEST-000001 succeeded,manifest_file_number is 1, next_file_number is 3, last_sequence is 0, log_number is 0,prev_log_number is 0,max_column_family is 0,min_log_number_to_keep is 0
2025/07/06-15:54:52.771714 6099709952 [db/version_set.cc:5722] Column family [default] (ID 0), log number is 0
2025/07/06-15:54:52.771737 6099709952 [db/db_impl/db_impl_open.cc:537] DB ID: 74499486-a383-481a-b8c5-0f202aaddf3a
2025/07/06-15:54:52.772047 6099709952 [db/version_set.cc:5180] Creating manifest 5
2025/07/06-15:54:52.772614 6099709952 [db/column_family.cc:621] --------------- Options for column family [configs]:
2025/07/06-15:54:52.772616 6099709952               Options.comparator: leveldb.BytewiseComparator
2025/07/06-15:54:52.772617 6099709952           Options.merge_operator: None
2025/07/06-15:54:52.772618 6099709952        Options.compaction_filter: None
2025/07/06-15:54:52.772619 6099709952        Options.compaction_filter_factory: None
2025/07/06-15:54:52.772620 6099709952  Options.sst_partitioner_factory: None
2025/07/06-15:54:52.772621 6099709952         Options.memtable_factory: SkipListFactory
2025/07/06-15:54:52.772622 6099709952            Options.table_factory: BlockBasedTable
2025/07/06-15:54:52.772631 6099709952            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x1446335c0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x144633618
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: 0x0
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/06-15:54:52.772633 6099709952        Options.write_buffer_size: 67108864
2025/07/06-15:54:52.772634 6099709952  Options.max_write_buffer_number: 2
2025/07/06-15:54:52.772635 6099709952          Options.compression: Snappy
2025/07/06-15:54:52.772636 6099709952                  Options.bottommost_compression: Disabled
2025/07/06-15:54:52.772637 6099709952       Options.prefix_extractor: nullptr
2025/07/06-15:54:52.772638 6099709952   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/06-15:54:52.772639 6099709952             Options.num_levels: 7
2025/07/06-15:54:52.772640 6099709952        Options.min_write_buffer_number_to_merge: 1
2025/07/06-15:54:52.772640 6099709952     Options.max_write_buffer_number_to_maintain: 0
2025/07/06-15:54:52.772641 6099709952     Options.max_write_buffer_size_to_maintain: 0
2025/07/06-15:54:52.772642 6099709952            Options.bottommost_compression_opts.window_bits: -14
2025/07/06-15:54:52.772643 6099709952                  Options.bottommost_compression_opts.level: 32767
2025/07/06-15:54:52.772644 6099709952               Options.bottommost_compression_opts.strategy: 0
2025/07/06-15:54:52.772645 6099709952         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/06-15:54:52.772646 6099709952         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/06-15:54:52.772647 6099709952         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/06-15:54:52.772648 6099709952                  Options.bottommost_compression_opts.enabled: false
2025/07/06-15:54:52.772649 6099709952         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/06-15:54:52.772650 6099709952         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/06-15:54:52.772651 6099709952            Options.compression_opts.window_bits: -14
2025/07/06-15:54:52.772652 6099709952                  Options.compression_opts.level: 32767
2025/07/06-15:54:52.772653 6099709952               Options.compression_opts.strategy: 0
2025/07/06-15:54:52.772654 6099709952         Options.compression_opts.max_dict_bytes: 0
2025/07/06-15:54:52.772655 6099709952         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/06-15:54:52.772656 6099709952         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/06-15:54:52.772657 6099709952         Options.compression_opts.parallel_threads: 1
2025/07/06-15:54:52.772658 6099709952                  Options.compression_opts.enabled: false
2025/07/06-15:54:52.772659 6099709952         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/06-15:54:52.772660 6099709952      Options.level0_file_num_compaction_trigger: 4
2025/07/06-15:54:52.772660 6099709952          Options.level0_slowdown_writes_trigger: 20
2025/07/06-15:54:52.772661 6099709952              Options.level0_stop_writes_trigger: 36
2025/07/06-15:54:52.772662 6099709952                   Options.target_file_size_base: 67108864
2025/07/06-15:54:52.772663 6099709952             Options.target_file_size_multiplier: 1
2025/07/06-15:54:52.772664 6099709952                Options.max_bytes_for_level_base: 268435456
2025/07/06-15:54:52.772665 6099709952 Options.level_compaction_dynamic_level_bytes: 0
2025/07/06-15:54:52.772666 6099709952          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/06-15:54:52.772667 6099709952 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/06-15:54:52.772668 6099709952 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/06-15:54:52.772669 6099709952 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/06-15:54:52.772670 6099709952 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/06-15:54:52.772671 6099709952 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/06-15:54:52.772672 6099709952 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/06-15:54:52.772672 6099709952 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/06-15:54:52.772673 6099709952       Options.max_sequential_skip_in_iterations: 8
2025/07/06-15:54:52.772674 6099709952                    Options.max_compaction_bytes: 1677721600
2025/07/06-15:54:52.772675 6099709952   Options.ignore_max_compaction_bytes_for_input: true
2025/07/06-15:54:52.772676 6099709952                        Options.arena_block_size: 1048576
2025/07/06-15:54:52.772677 6099709952   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/06-15:54:52.772678 6099709952   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/06-15:54:52.772679 6099709952                Options.disable_auto_compactions: 0
2025/07/06-15:54:52.772680 6099709952                        Options.compaction_style: kCompactionStyleLevel
2025/07/06-15:54:52.772682 6099709952                          Options.compaction_pri: kMinOverlappingRatio
2025/07/06-15:54:52.772683 6099709952 Options.compaction_options_universal.size_ratio: 1
2025/07/06-15:54:52.772683 6099709952 Options.compaction_options_universal.min_merge_width: 2
2025/07/06-15:54:52.772684 6099709952 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/06-15:54:52.772685 6099709952 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/06-15:54:52.772686 6099709952 Options.compaction_options_universal.compression_size_percent: -1
2025/07/06-15:54:52.772688 6099709952 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/06-15:54:52.772688 6099709952 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/06-15:54:52.772689 6099709952 Options.compaction_options_fifo.allow_compaction: 0
2025/07/06-15:54:52.772691 6099709952                   Options.table_properties_collectors: 
2025/07/06-15:54:52.772691 6099709952                   Options.inplace_update_support: 0
2025/07/06-15:54:52.772692 6099709952                 Options.inplace_update_num_locks: 10000
2025/07/06-15:54:52.772693 6099709952               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/06-15:54:52.772694 6099709952               Options.memtable_whole_key_filtering: 0
2025/07/06-15:54:52.772695 6099709952   Options.memtable_huge_page_size: 0
2025/07/06-15:54:52.772696 6099709952                           Options.bloom_locality: 0
2025/07/06-15:54:52.772697 6099709952                    Options.max_successive_merges: 0
2025/07/06-15:54:52.772698 6099709952                Options.optimize_filters_for_hits: 0
2025/07/06-15:54:52.772699 6099709952                Options.paranoid_file_checks: 0
2025/07/06-15:54:52.772700 6099709952                Options.force_consistency_checks: 1
2025/07/06-15:54:52.772701 6099709952                Options.report_bg_io_stats: 0
2025/07/06-15:54:52.772701 6099709952                               Options.ttl: 2592000
2025/07/06-15:54:52.772702 6099709952          Options.periodic_compaction_seconds: 0
2025/07/06-15:54:52.772703 6099709952  Options.preclude_last_level_data_seconds: 0
2025/07/06-15:54:52.772704 6099709952    Options.preserve_internal_time_seconds: 0
2025/07/06-15:54:52.772705 6099709952                       Options.enable_blob_files: false
2025/07/06-15:54:52.772706 6099709952                           Options.min_blob_size: 0
2025/07/06-15:54:52.772707 6099709952                          Options.blob_file_size: 268435456
2025/07/06-15:54:52.772708 6099709952                   Options.blob_compression_type: NoCompression
2025/07/06-15:54:52.772709 6099709952          Options.enable_blob_garbage_collection: false
2025/07/06-15:54:52.772710 6099709952      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/06-15:54:52.772711 6099709952 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/06-15:54:52.772712 6099709952          Options.blob_compaction_readahead_size: 0
2025/07/06-15:54:52.772713 6099709952                Options.blob_file_starting_level: 0
2025/07/06-15:54:52.772713 6099709952 Options.experimental_mempurge_threshold: 0.000000
2025/07/06-15:54:52.772760 6099709952 [db/db_impl/db_impl.cc:3200] Created column family [configs] (ID 1)
2025/07/06-15:54:52.775894 6099709952 [db/column_family.cc:621] --------------- Options for column family [versions]:
2025/07/06-15:54:52.775896 6099709952               Options.comparator: leveldb.BytewiseComparator
2025/07/06-15:54:52.775897 6099709952           Options.merge_operator: None
2025/07/06-15:54:52.775898 6099709952        Options.compaction_filter: None
2025/07/06-15:54:52.775899 6099709952        Options.compaction_filter_factory: None
2025/07/06-15:54:52.775900 6099709952  Options.sst_partitioner_factory: None
2025/07/06-15:54:52.775900 6099709952         Options.memtable_factory: SkipListFactory
2025/07/06-15:54:52.775901 6099709952            Options.table_factory: BlockBasedTable
2025/07/06-15:54:52.775956 6099709952            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x1446344b0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x144634508
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: 0x0
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/06-15:54:52.775958 6099709952        Options.write_buffer_size: 67108864
2025/07/06-15:54:52.775959 6099709952  Options.max_write_buffer_number: 2
2025/07/06-15:54:52.775960 6099709952          Options.compression: Snappy
2025/07/06-15:54:52.775961 6099709952                  Options.bottommost_compression: Disabled
2025/07/06-15:54:52.775963 6099709952       Options.prefix_extractor: nullptr
2025/07/06-15:54:52.775964 6099709952   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/06-15:54:52.775965 6099709952             Options.num_levels: 7
2025/07/06-15:54:52.775966 6099709952        Options.min_write_buffer_number_to_merge: 1
2025/07/06-15:54:52.775967 6099709952     Options.max_write_buffer_number_to_maintain: 0
2025/07/06-15:54:52.775968 6099709952     Options.max_write_buffer_size_to_maintain: 0
2025/07/06-15:54:52.775969 6099709952            Options.bottommost_compression_opts.window_bits: -14
2025/07/06-15:54:52.775971 6099709952                  Options.bottommost_compression_opts.level: 32767
2025/07/06-15:54:52.775973 6099709952               Options.bottommost_compression_opts.strategy: 0
2025/07/06-15:54:52.775974 6099709952         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/06-15:54:52.775975 6099709952         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/06-15:54:52.775976 6099709952         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/06-15:54:52.775977 6099709952                  Options.bottommost_compression_opts.enabled: false
2025/07/06-15:54:52.775978 6099709952         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/06-15:54:52.775979 6099709952         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/06-15:54:52.775980 6099709952            Options.compression_opts.window_bits: -14
2025/07/06-15:54:52.775981 6099709952                  Options.compression_opts.level: 32767
2025/07/06-15:54:52.775982 6099709952               Options.compression_opts.strategy: 0
2025/07/06-15:54:52.775983 6099709952         Options.compression_opts.max_dict_bytes: 0
2025/07/06-15:54:52.775984 6099709952         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/06-15:54:52.775985 6099709952         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/06-15:54:52.775987 6099709952         Options.compression_opts.parallel_threads: 1
2025/07/06-15:54:52.775988 6099709952                  Options.compression_opts.enabled: false
2025/07/06-15:54:52.775989 6099709952         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/06-15:54:52.775990 6099709952      Options.level0_file_num_compaction_trigger: 4
2025/07/06-15:54:52.775991 6099709952          Options.level0_slowdown_writes_trigger: 20
2025/07/06-15:54:52.775992 6099709952              Options.level0_stop_writes_trigger: 36
2025/07/06-15:54:52.775993 6099709952                   Options.target_file_size_base: 67108864
2025/07/06-15:54:52.775994 6099709952             Options.target_file_size_multiplier: 1
2025/07/06-15:54:52.775994 6099709952                Options.max_bytes_for_level_base: 268435456
2025/07/06-15:54:52.775995 6099709952 Options.level_compaction_dynamic_level_bytes: 0
2025/07/06-15:54:52.775997 6099709952          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/06-15:54:52.775998 6099709952 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/06-15:54:52.775999 6099709952 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/06-15:54:52.776000 6099709952 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/06-15:54:52.776001 6099709952 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/06-15:54:52.776002 6099709952 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/06-15:54:52.776003 6099709952 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/06-15:54:52.776004 6099709952 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/06-15:54:52.776005 6099709952       Options.max_sequential_skip_in_iterations: 8
2025/07/06-15:54:52.776006 6099709952                    Options.max_compaction_bytes: 1677721600
2025/07/06-15:54:52.776007 6099709952   Options.ignore_max_compaction_bytes_for_input: true
2025/07/06-15:54:52.776009 6099709952                        Options.arena_block_size: 1048576
2025/07/06-15:54:52.776010 6099709952   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/06-15:54:52.776011 6099709952   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/06-15:54:52.776012 6099709952                Options.disable_auto_compactions: 0
2025/07/06-15:54:52.776013 6099709952                        Options.compaction_style: kCompactionStyleLevel
2025/07/06-15:54:52.776015 6099709952                          Options.compaction_pri: kMinOverlappingRatio
2025/07/06-15:54:52.776016 6099709952 Options.compaction_options_universal.size_ratio: 1
2025/07/06-15:54:52.776017 6099709952 Options.compaction_options_universal.min_merge_width: 2
2025/07/06-15:54:52.776018 6099709952 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/06-15:54:52.776019 6099709952 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/06-15:54:52.776020 6099709952 Options.compaction_options_universal.compression_size_percent: -1
2025/07/06-15:54:52.776021 6099709952 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/06-15:54:52.776022 6099709952 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/06-15:54:52.776023 6099709952 Options.compaction_options_fifo.allow_compaction: 0
2025/07/06-15:54:52.776024 6099709952                   Options.table_properties_collectors: 
2025/07/06-15:54:52.776025 6099709952                   Options.inplace_update_support: 0
2025/07/06-15:54:52.776026 6099709952                 Options.inplace_update_num_locks: 10000
2025/07/06-15:54:52.776027 6099709952               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/06-15:54:52.776028 6099709952               Options.memtable_whole_key_filtering: 0
2025/07/06-15:54:52.776029 6099709952   Options.memtable_huge_page_size: 0
2025/07/06-15:54:52.776029 6099709952                           Options.bloom_locality: 0
2025/07/06-15:54:52.776030 6099709952                    Options.max_successive_merges: 0
2025/07/06-15:54:52.776031 6099709952                Options.optimize_filters_for_hits: 0
2025/07/06-15:54:52.776032 6099709952                Options.paranoid_file_checks: 0
2025/07/06-15:54:52.776033 6099709952                Options.force_consistency_checks: 1
2025/07/06-15:54:52.776034 6099709952                Options.report_bg_io_stats: 0
2025/07/06-15:54:52.776035 6099709952                               Options.ttl: 2592000
2025/07/06-15:54:52.776036 6099709952          Options.periodic_compaction_seconds: 0
2025/07/06-15:54:52.776036 6099709952  Options.preclude_last_level_data_seconds: 0
2025/07/06-15:54:52.776037 6099709952    Options.preserve_internal_time_seconds: 0
2025/07/06-15:54:52.776038 6099709952                       Options.enable_blob_files: false
2025/07/06-15:54:52.776039 6099709952                           Options.min_blob_size: 0
2025/07/06-15:54:52.776040 6099709952                          Options.blob_file_size: 268435456
2025/07/06-15:54:52.776041 6099709952                   Options.blob_compression_type: NoCompression
2025/07/06-15:54:52.776042 6099709952          Options.enable_blob_garbage_collection: false
2025/07/06-15:54:52.776043 6099709952      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/06-15:54:52.776044 6099709952 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/06-15:54:52.776044 6099709952          Options.blob_compaction_readahead_size: 0
2025/07/06-15:54:52.776045 6099709952                Options.blob_file_starting_level: 0
2025/07/06-15:54:52.776046 6099709952 Options.experimental_mempurge_threshold: 0.000000
2025/07/06-15:54:52.776086 6099709952 [db/db_impl/db_impl.cc:3200] Created column family [versions] (ID 2)
2025/07/06-15:54:52.779535 6099709952 [db/column_family.cc:621] --------------- Options for column family [logs]:
2025/07/06-15:54:52.779537 6099709952               Options.comparator: leveldb.BytewiseComparator
2025/07/06-15:54:52.779538 6099709952           Options.merge_operator: None
2025/07/06-15:54:52.779539 6099709952        Options.compaction_filter: None
2025/07/06-15:54:52.779540 6099709952        Options.compaction_filter_factory: None
2025/07/06-15:54:52.779541 6099709952  Options.sst_partitioner_factory: None
2025/07/06-15:54:52.779542 6099709952         Options.memtable_factory: SkipListFactory
2025/07/06-15:54:52.779543 6099709952            Options.table_factory: BlockBasedTable
2025/07/06-15:54:52.779551 6099709952            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x1446353a0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x1446353f8
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: 0x0
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/06-15:54:52.779553 6099709952        Options.write_buffer_size: 67108864
2025/07/06-15:54:52.779553 6099709952  Options.max_write_buffer_number: 2
2025/07/06-15:54:52.779554 6099709952          Options.compression: Snappy
2025/07/06-15:54:52.779555 6099709952                  Options.bottommost_compression: Disabled
2025/07/06-15:54:52.779556 6099709952       Options.prefix_extractor: nullptr
2025/07/06-15:54:52.779557 6099709952   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/06-15:54:52.779558 6099709952             Options.num_levels: 7
2025/07/06-15:54:52.779559 6099709952        Options.min_write_buffer_number_to_merge: 1
2025/07/06-15:54:52.779560 6099709952     Options.max_write_buffer_number_to_maintain: 0
2025/07/06-15:54:52.779561 6099709952     Options.max_write_buffer_size_to_maintain: 0
2025/07/06-15:54:52.779562 6099709952            Options.bottommost_compression_opts.window_bits: -14
2025/07/06-15:54:52.779563 6099709952                  Options.bottommost_compression_opts.level: 32767
2025/07/06-15:54:52.779563 6099709952               Options.bottommost_compression_opts.strategy: 0
2025/07/06-15:54:52.779564 6099709952         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/06-15:54:52.779565 6099709952         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/06-15:54:52.779566 6099709952         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/06-15:54:52.779567 6099709952                  Options.bottommost_compression_opts.enabled: false
2025/07/06-15:54:52.779568 6099709952         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/06-15:54:52.779569 6099709952         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/06-15:54:52.779570 6099709952            Options.compression_opts.window_bits: -14
2025/07/06-15:54:52.779571 6099709952                  Options.compression_opts.level: 32767
2025/07/06-15:54:52.779571 6099709952               Options.compression_opts.strategy: 0
2025/07/06-15:54:52.779572 6099709952         Options.compression_opts.max_dict_bytes: 0
2025/07/06-15:54:52.779573 6099709952         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/06-15:54:52.779574 6099709952         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/06-15:54:52.779575 6099709952         Options.compression_opts.parallel_threads: 1
2025/07/06-15:54:52.779576 6099709952                  Options.compression_opts.enabled: false
2025/07/06-15:54:52.779576 6099709952         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/06-15:54:52.779577 6099709952      Options.level0_file_num_compaction_trigger: 4
2025/07/06-15:54:52.779578 6099709952          Options.level0_slowdown_writes_trigger: 20
2025/07/06-15:54:52.779579 6099709952              Options.level0_stop_writes_trigger: 36
2025/07/06-15:54:52.779580 6099709952                   Options.target_file_size_base: 67108864
2025/07/06-15:54:52.779581 6099709952             Options.target_file_size_multiplier: 1
2025/07/06-15:54:52.779581 6099709952                Options.max_bytes_for_level_base: 268435456
2025/07/06-15:54:52.779582 6099709952 Options.level_compaction_dynamic_level_bytes: 0
2025/07/06-15:54:52.779583 6099709952          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/06-15:54:52.779584 6099709952 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/06-15:54:52.779585 6099709952 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/06-15:54:52.779586 6099709952 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/06-15:54:52.779587 6099709952 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/06-15:54:52.779588 6099709952 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/06-15:54:52.779589 6099709952 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/06-15:54:52.779589 6099709952 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/06-15:54:52.779590 6099709952       Options.max_sequential_skip_in_iterations: 8
2025/07/06-15:54:52.779591 6099709952                    Options.max_compaction_bytes: 1677721600
2025/07/06-15:54:52.779592 6099709952   Options.ignore_max_compaction_bytes_for_input: true
2025/07/06-15:54:52.779593 6099709952                        Options.arena_block_size: 1048576
2025/07/06-15:54:52.779593 6099709952   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/06-15:54:52.779594 6099709952   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/06-15:54:52.779595 6099709952                Options.disable_auto_compactions: 0
2025/07/06-15:54:52.779596 6099709952                        Options.compaction_style: kCompactionStyleLevel
2025/07/06-15:54:52.779598 6099709952                          Options.compaction_pri: kMinOverlappingRatio
2025/07/06-15:54:52.779598 6099709952 Options.compaction_options_universal.size_ratio: 1
2025/07/06-15:54:52.779599 6099709952 Options.compaction_options_universal.min_merge_width: 2
2025/07/06-15:54:52.779600 6099709952 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/06-15:54:52.779601 6099709952 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/06-15:54:52.779602 6099709952 Options.compaction_options_universal.compression_size_percent: -1
2025/07/06-15:54:52.779603 6099709952 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/06-15:54:52.779604 6099709952 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/06-15:54:52.779605 6099709952 Options.compaction_options_fifo.allow_compaction: 0
2025/07/06-15:54:52.779606 6099709952                   Options.table_properties_collectors: 
2025/07/06-15:54:52.779607 6099709952                   Options.inplace_update_support: 0
2025/07/06-15:54:52.779607 6099709952                 Options.inplace_update_num_locks: 10000
2025/07/06-15:54:52.779608 6099709952               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/06-15:54:52.779609 6099709952               Options.memtable_whole_key_filtering: 0
2025/07/06-15:54:52.779610 6099709952   Options.memtable_huge_page_size: 0
2025/07/06-15:54:52.779611 6099709952                           Options.bloom_locality: 0
2025/07/06-15:54:52.779612 6099709952                    Options.max_successive_merges: 0
2025/07/06-15:54:52.779613 6099709952                Options.optimize_filters_for_hits: 0
2025/07/06-15:54:52.779613 6099709952                Options.paranoid_file_checks: 0
2025/07/06-15:54:52.779614 6099709952                Options.force_consistency_checks: 1
2025/07/06-15:54:52.779615 6099709952                Options.report_bg_io_stats: 0
2025/07/06-15:54:52.779616 6099709952                               Options.ttl: 2592000
2025/07/06-15:54:52.779617 6099709952          Options.periodic_compaction_seconds: 0
2025/07/06-15:54:52.779618 6099709952  Options.preclude_last_level_data_seconds: 0
2025/07/06-15:54:52.779619 6099709952    Options.preserve_internal_time_seconds: 0
2025/07/06-15:54:52.779619 6099709952                       Options.enable_blob_files: false
2025/07/06-15:54:52.779620 6099709952                           Options.min_blob_size: 0
2025/07/06-15:54:52.779621 6099709952                          Options.blob_file_size: 268435456
2025/07/06-15:54:52.779622 6099709952                   Options.blob_compression_type: NoCompression
2025/07/06-15:54:52.779623 6099709952          Options.enable_blob_garbage_collection: false
2025/07/06-15:54:52.779624 6099709952      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/06-15:54:52.779625 6099709952 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/06-15:54:52.779626 6099709952          Options.blob_compaction_readahead_size: 0
2025/07/06-15:54:52.779627 6099709952                Options.blob_file_starting_level: 0
2025/07/06-15:54:52.779628 6099709952 Options.experimental_mempurge_threshold: 0.000000
2025/07/06-15:54:52.779673 6099709952 [db/db_impl/db_impl.cc:3200] Created column family [logs] (ID 3)
2025/07/06-15:54:52.783881 6099709952 [db/column_family.cc:621] --------------- Options for column family [meta]:
2025/07/06-15:54:52.783883 6099709952               Options.comparator: leveldb.BytewiseComparator
2025/07/06-15:54:52.783884 6099709952           Options.merge_operator: None
2025/07/06-15:54:52.783885 6099709952        Options.compaction_filter: None
2025/07/06-15:54:52.783886 6099709952        Options.compaction_filter_factory: None
2025/07/06-15:54:52.783887 6099709952  Options.sst_partitioner_factory: None
2025/07/06-15:54:52.783888 6099709952         Options.memtable_factory: SkipListFactory
2025/07/06-15:54:52.783889 6099709952            Options.table_factory: BlockBasedTable
2025/07/06-15:54:52.783898 6099709952            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x144636290)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x1446362e8
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: 0x0
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/06-15:54:52.783899 6099709952        Options.write_buffer_size: 67108864
2025/07/06-15:54:52.783900 6099709952  Options.max_write_buffer_number: 2
2025/07/06-15:54:52.783901 6099709952          Options.compression: Snappy
2025/07/06-15:54:52.783902 6099709952                  Options.bottommost_compression: Disabled
2025/07/06-15:54:52.783903 6099709952       Options.prefix_extractor: nullptr
2025/07/06-15:54:52.783904 6099709952   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/06-15:54:52.783905 6099709952             Options.num_levels: 7
2025/07/06-15:54:52.783906 6099709952        Options.min_write_buffer_number_to_merge: 1
2025/07/06-15:54:52.783907 6099709952     Options.max_write_buffer_number_to_maintain: 0
2025/07/06-15:54:52.783908 6099709952     Options.max_write_buffer_size_to_maintain: 0
2025/07/06-15:54:52.783908 6099709952            Options.bottommost_compression_opts.window_bits: -14
2025/07/06-15:54:52.783909 6099709952                  Options.bottommost_compression_opts.level: 32767
2025/07/06-15:54:52.783910 6099709952               Options.bottommost_compression_opts.strategy: 0
2025/07/06-15:54:52.783911 6099709952         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/06-15:54:52.783912 6099709952         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/06-15:54:52.783913 6099709952         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/06-15:54:52.783914 6099709952                  Options.bottommost_compression_opts.enabled: false
2025/07/06-15:54:52.783915 6099709952         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/06-15:54:52.783916 6099709952         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/06-15:54:52.783917 6099709952            Options.compression_opts.window_bits: -14
2025/07/06-15:54:52.783918 6099709952                  Options.compression_opts.level: 32767
2025/07/06-15:54:52.783918 6099709952               Options.compression_opts.strategy: 0
2025/07/06-15:54:52.783919 6099709952         Options.compression_opts.max_dict_bytes: 0
2025/07/06-15:54:52.783920 6099709952         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/06-15:54:52.783921 6099709952         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/06-15:54:52.783922 6099709952         Options.compression_opts.parallel_threads: 1
2025/07/06-15:54:52.783923 6099709952                  Options.compression_opts.enabled: false
2025/07/06-15:54:52.783924 6099709952         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/06-15:54:52.783925 6099709952      Options.level0_file_num_compaction_trigger: 4
2025/07/06-15:54:52.783925 6099709952          Options.level0_slowdown_writes_trigger: 20
2025/07/06-15:54:52.783926 6099709952              Options.level0_stop_writes_trigger: 36
2025/07/06-15:54:52.783927 6099709952                   Options.target_file_size_base: 67108864
2025/07/06-15:54:52.783928 6099709952             Options.target_file_size_multiplier: 1
2025/07/06-15:54:52.783929 6099709952                Options.max_bytes_for_level_base: 268435456
2025/07/06-15:54:52.783930 6099709952 Options.level_compaction_dynamic_level_bytes: 0
2025/07/06-15:54:52.783931 6099709952          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/06-15:54:52.783932 6099709952 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/06-15:54:52.783933 6099709952 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/06-15:54:52.783933 6099709952 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/06-15:54:52.783934 6099709952 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/06-15:54:52.783935 6099709952 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/06-15:54:52.783936 6099709952 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/06-15:54:52.783937 6099709952 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/06-15:54:52.783938 6099709952       Options.max_sequential_skip_in_iterations: 8
2025/07/06-15:54:52.783939 6099709952                    Options.max_compaction_bytes: 1677721600
2025/07/06-15:54:52.783939 6099709952   Options.ignore_max_compaction_bytes_for_input: true
2025/07/06-15:54:52.783940 6099709952                        Options.arena_block_size: 1048576
2025/07/06-15:54:52.783941 6099709952   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/06-15:54:52.783942 6099709952   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/06-15:54:52.783943 6099709952                Options.disable_auto_compactions: 0
2025/07/06-15:54:52.783944 6099709952                        Options.compaction_style: kCompactionStyleLevel
2025/07/06-15:54:52.783945 6099709952                          Options.compaction_pri: kMinOverlappingRatio
2025/07/06-15:54:52.783946 6099709952 Options.compaction_options_universal.size_ratio: 1
2025/07/06-15:54:52.783947 6099709952 Options.compaction_options_universal.min_merge_width: 2
2025/07/06-15:54:52.783948 6099709952 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/06-15:54:52.783949 6099709952 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/06-15:54:52.783950 6099709952 Options.compaction_options_universal.compression_size_percent: -1
2025/07/06-15:54:52.783951 6099709952 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/06-15:54:52.783952 6099709952 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/06-15:54:52.783953 6099709952 Options.compaction_options_fifo.allow_compaction: 0
2025/07/06-15:54:52.783954 6099709952                   Options.table_properties_collectors: 
2025/07/06-15:54:52.783955 6099709952                   Options.inplace_update_support: 0
2025/07/06-15:54:52.783955 6099709952                 Options.inplace_update_num_locks: 10000
2025/07/06-15:54:52.783956 6099709952               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/06-15:54:52.783957 6099709952               Options.memtable_whole_key_filtering: 0
2025/07/06-15:54:52.783958 6099709952   Options.memtable_huge_page_size: 0
2025/07/06-15:54:52.783959 6099709952                           Options.bloom_locality: 0
2025/07/06-15:54:52.783960 6099709952                    Options.max_successive_merges: 0
2025/07/06-15:54:52.783961 6099709952                Options.optimize_filters_for_hits: 0
2025/07/06-15:54:52.783961 6099709952                Options.paranoid_file_checks: 0
2025/07/06-15:54:52.783962 6099709952                Options.force_consistency_checks: 1
2025/07/06-15:54:52.783963 6099709952                Options.report_bg_io_stats: 0
2025/07/06-15:54:52.783964 6099709952                               Options.ttl: 2592000
2025/07/06-15:54:52.783965 6099709952          Options.periodic_compaction_seconds: 0
2025/07/06-15:54:52.783966 6099709952  Options.preclude_last_level_data_seconds: 0
2025/07/06-15:54:52.783967 6099709952    Options.preserve_internal_time_seconds: 0
2025/07/06-15:54:52.783967 6099709952                       Options.enable_blob_files: false
2025/07/06-15:54:52.783968 6099709952                           Options.min_blob_size: 0
2025/07/06-15:54:52.783969 6099709952                          Options.blob_file_size: 268435456
2025/07/06-15:54:52.783970 6099709952                   Options.blob_compression_type: NoCompression
2025/07/06-15:54:52.783971 6099709952          Options.enable_blob_garbage_collection: false
2025/07/06-15:54:52.783972 6099709952      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/06-15:54:52.783973 6099709952 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/06-15:54:52.783974 6099709952          Options.blob_compaction_readahead_size: 0
2025/07/06-15:54:52.783974 6099709952                Options.blob_file_starting_level: 0
2025/07/06-15:54:52.783975 6099709952 Options.experimental_mempurge_threshold: 0.000000
2025/07/06-15:54:52.784015 6099709952 [db/db_impl/db_impl.cc:3200] Created column family [meta] (ID 4)
2025/07/06-15:54:52.794004 6099709952 [db/db_impl/db_impl_open.cc:1977] SstFileManager instance 0x14620aa10
2025/07/06-15:54:52.794044 6099709952 DB pointer 0x14781d800
2025/07/06-15:54:52.794662 6093844480 [db/db_impl/db_impl.cc:1085] ------- DUMPING STATS -------
2025/07/06-15:54:52.794665 6093844480 [db/db_impl/db_impl.cc:1086] 
** DB Stats **
Uptime(secs): 0.0 total, 0.0 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0, 
** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0, Block cache LRUCache@0x144637278#15942 capacity: 8.00 MB usage: 0.08 KB table_size: 256 occupancy: 87 collections: 1 last_copies: 0 last_secs: 3.5e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** Compaction Stats [configs] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [configs] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0, Block cache LRUCache@0x144633618#15942 capacity: 8.00 MB usage: 0.08 KB table_size: 256 occupancy: 87 collections: 1 last_copies: 0 last_secs: 2.8e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [configs] **

** Compaction Stats [versions] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [versions] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0, Block cache LRUCache@0x144634508#15942 capacity: 8.00 MB usage: 0.08 KB table_size: 256 occupancy: 87 collections: 1 last_copies: 0 last_secs: 2.9e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [versions] **

** Compaction Stats [logs] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [logs] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0, Block cache LRUCache@0x1446353f8#15942 capacity: 8.00 MB usage: 0.08 KB table_size: 256 occupancy: 87 collections: 1 last_copies: 0 last_secs: 2.7e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [logs] **

** Compaction Stats [meta] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [meta] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0, Block cache LRUCache@0x1446362e8#15942 capacity: 8.00 MB usage: 0.08 KB table_size: 256 occupancy: 87 collections: 1 last_copies: 0 last_secs: 2.9e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [meta] **
2025/07/06-15:54:52.794717 6099709952 [db/db_impl/db_impl.cc:490] Shutdown: canceling all background work
2025/07/06-15:54:52.794971 6099709952 [db/db_impl/db_impl.cc:692] Shutdown complete
