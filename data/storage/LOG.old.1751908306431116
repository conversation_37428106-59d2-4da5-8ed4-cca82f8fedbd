2025/07/08-01:11:46.431660 6118289408 RocksDB version: 9.9.3
2025/07/08-01:11:46.432898 6103265280 Compile date 2024-12-05 01:25:31
2025/07/08-01:11:46.432904 6103265280 DB SUMMARY
2025/07/08-01:11:46.432906 6103265280 Host name (Env):  Mac
2025/07/08-01:11:46.432911 6103265280 DB Session ID:  YBJ53X3IYPLKVHJXVM63
2025/07/08-01:11:46.432973 6103265280 SST files in ./data/storage dir, Total Num: 0, files: 
2025/07/08-01:11:46.432977 6103265280 Write Ahead Log file in ./data/storage: 
2025/07/08-01:11:46.432981 6103265280                         Options.error_if_exists: 0
2025/07/08-01:11:46.432991 6103265280                       Options.create_if_missing: 1
2025/07/08-01:11:46.433002 6103265280                         Options.paranoid_checks: 1
2025/07/08-01:11:46.433007 6103265280             Options.flush_verify_memtable_count: 1
2025/07/08-01:11:46.433012 6103265280          Options.compaction_verify_record_count: 1
2025/07/08-01:11:46.433013 6103265280                               Options.track_and_verify_wals_in_manifest: 0
2025/07/08-01:11:46.433018 6103265280        Options.verify_sst_unique_id_in_manifest: 1
2025/07/08-01:11:46.433020 6103265280                                     Options.env: 0x121c09700
2025/07/08-01:11:46.433027 6103265280                                      Options.fs: PosixFileSystem
2025/07/08-01:11:46.433033 6103265280                                Options.info_log: 0x121c17498
2025/07/08-01:11:46.433035 6103265280                Options.max_file_opening_threads: 16
2025/07/08-01:11:46.433048 6103265280                              Options.statistics: 0x0
2025/07/08-01:11:46.433050 6103265280                               Options.use_fsync: 0
2025/07/08-01:11:46.433055 6103265280                       Options.max_log_file_size: 0
2025/07/08-01:11:46.433056 6103265280                  Options.max_manifest_file_size: 1073741824
2025/07/08-01:11:46.433068 6103265280                   Options.log_file_time_to_roll: 0
2025/07/08-01:11:46.433076 6103265280                       Options.keep_log_file_num: 1000
2025/07/08-01:11:46.433077 6103265280                    Options.recycle_log_file_num: 0
2025/07/08-01:11:46.433101 6103265280                         Options.allow_fallocate: 1
2025/07/08-01:11:46.433103 6103265280                        Options.allow_mmap_reads: 0
2025/07/08-01:11:46.433114 6103265280                       Options.allow_mmap_writes: 0
2025/07/08-01:11:46.433124 6103265280                        Options.use_direct_reads: 0
2025/07/08-01:11:46.433131 6103265280                        Options.use_direct_io_for_flush_and_compaction: 0
2025/07/08-01:11:46.433133 6103265280          Options.create_missing_column_families: 1
2025/07/08-01:11:46.433144 6103265280                              Options.db_log_dir: 
2025/07/08-01:11:46.433152 6103265280                                 Options.wal_dir: 
2025/07/08-01:11:46.433154 6103265280                Options.table_cache_numshardbits: 6
2025/07/08-01:11:46.433155 6103265280                         Options.WAL_ttl_seconds: 0
2025/07/08-01:11:46.433163 6103265280                       Options.WAL_size_limit_MB: 0
2025/07/08-01:11:46.433165 6103265280                        Options.max_write_batch_group_size_bytes: 1048576
2025/07/08-01:11:46.433174 6103265280             Options.manifest_preallocation_size: 4194304
2025/07/08-01:11:46.433175 6103265280                     Options.is_fd_close_on_exec: 1
2025/07/08-01:11:46.433176 6103265280                   Options.advise_random_on_open: 1
2025/07/08-01:11:46.433184 6103265280                    Options.db_write_buffer_size: 0
2025/07/08-01:11:46.433193 6103265280                    Options.write_buffer_manager: 0x109a0b0a0
2025/07/08-01:11:46.433195 6103265280           Options.random_access_max_buffer_size: 1048576
2025/07/08-01:11:46.433197 6103265280                      Options.use_adaptive_mutex: 0
2025/07/08-01:11:46.433203 6103265280                            Options.rate_limiter: 0x0
2025/07/08-01:11:46.433210 6103265280     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/07/08-01:11:46.433211 6103265280                       Options.wal_recovery_mode: 2
2025/07/08-01:11:46.433213 6103265280                  Options.enable_thread_tracking: 0
2025/07/08-01:11:46.433215 6103265280                  Options.enable_pipelined_write: 0
2025/07/08-01:11:46.433216 6103265280                  Options.unordered_write: 0
2025/07/08-01:11:46.433220 6103265280         Options.allow_concurrent_memtable_write: 1
2025/07/08-01:11:46.433237 6103265280      Options.enable_write_thread_adaptive_yield: 1
2025/07/08-01:11:46.433238 6103265280             Options.write_thread_max_yield_usec: 100
2025/07/08-01:11:46.433239 6103265280            Options.write_thread_slow_yield_usec: 3
2025/07/08-01:11:46.433253 6103265280                               Options.row_cache: None
2025/07/08-01:11:46.433254 6103265280                              Options.wal_filter: None
2025/07/08-01:11:46.433283 6103265280             Options.avoid_flush_during_recovery: 0
2025/07/08-01:11:46.433285 6103265280             Options.allow_ingest_behind: 0
2025/07/08-01:11:46.433286 6103265280             Options.two_write_queues: 0
2025/07/08-01:11:46.433315 6103265280             Options.manual_wal_flush: 0
2025/07/08-01:11:46.433316 6103265280             Options.wal_compression: 0
2025/07/08-01:11:46.433317 6103265280             Options.background_close_inactive_wals: 0
2025/07/08-01:11:46.433319 6103265280             Options.atomic_flush: 0
2025/07/08-01:11:46.433321 6103265280             Options.avoid_unnecessary_blocking_io: 0
2025/07/08-01:11:46.433336 6103265280             Options.prefix_seek_opt_in_only: 0
2025/07/08-01:11:46.433338 6103265280                 Options.persist_stats_to_disk: 0
2025/07/08-01:11:46.433344 6103265280                 Options.write_dbid_to_manifest: 1
2025/07/08-01:11:46.433345 6103265280                 Options.write_identity_file: 1
2025/07/08-01:11:46.433347 6103265280                 Options.log_readahead_size: 0
2025/07/08-01:11:46.433352 6103265280                 Options.file_checksum_gen_factory: Unknown
2025/07/08-01:11:46.433364 6103265280                 Options.best_efforts_recovery: 0
2025/07/08-01:11:46.433366 6103265280                Options.max_bgerror_resume_count: 2147483647
2025/07/08-01:11:46.433367 6103265280            Options.bgerror_resume_retry_interval: 1000000
2025/07/08-01:11:46.433375 6103265280             Options.allow_data_in_errors: 0
2025/07/08-01:11:46.433376 6103265280             Options.db_host_id: __hostname__
2025/07/08-01:11:46.433386 6103265280             Options.enforce_single_del_contracts: true
2025/07/08-01:11:46.433392 6103265280             Options.metadata_write_temperature: kUnknown
2025/07/08-01:11:46.433399 6103265280             Options.wal_write_temperature: kUnknown
2025/07/08-01:11:46.433406 6103265280             Options.max_background_jobs: 2
2025/07/08-01:11:46.433412 6103265280             Options.max_background_compactions: -1
2025/07/08-01:11:46.433433 6103265280             Options.max_subcompactions: 1
2025/07/08-01:11:46.433435 6103265280             Options.avoid_flush_during_shutdown: 0
2025/07/08-01:11:46.433444 6103265280           Options.writable_file_max_buffer_size: 1048576
2025/07/08-01:11:46.433461 6103265280             Options.delayed_write_rate : 16777216
2025/07/08-01:11:46.433475 6103265280             Options.max_total_wal_size: 0
2025/07/08-01:11:46.433489 6103265280             Options.delete_obsolete_files_period_micros: 21600000000
2025/07/08-01:11:46.433491 6103265280                   Options.stats_dump_period_sec: 600
2025/07/08-01:11:46.433499 6103265280                 Options.stats_persist_period_sec: 600
2025/07/08-01:11:46.433503 6103265280                 Options.stats_history_buffer_size: 1048576
2025/07/08-01:11:46.433511 6103265280                          Options.max_open_files: -1
2025/07/08-01:11:46.433518 6103265280                          Options.bytes_per_sync: 0
2025/07/08-01:11:46.433521 6103265280                      Options.wal_bytes_per_sync: 0
2025/07/08-01:11:46.433523 6103265280                   Options.strict_bytes_per_sync: 0
2025/07/08-01:11:46.433542 6103265280       Options.compaction_readahead_size: 2097152
2025/07/08-01:11:46.433544 6103265280                  Options.max_background_flushes: -1
2025/07/08-01:11:46.433545 6103265280 Options.daily_offpeak_time_utc: 
2025/07/08-01:11:46.433548 6103265280 Compression algorithms supported:
2025/07/08-01:11:46.433566 6103265280 	kZSTD supported: 1
2025/07/08-01:11:46.433568 6103265280 	kZlibCompression supported: 1
2025/07/08-01:11:46.433580 6103265280 	kXpressCompression supported: 0
2025/07/08-01:11:46.433587 6103265280 	kSnappyCompression supported: 1
2025/07/08-01:11:46.433590 6103265280 	kZSTDNotFinalCompression supported: 1
2025/07/08-01:11:46.433599 6103265280 	kLZ4HCCompression supported: 1
2025/07/08-01:11:46.433606 6103265280 	kLZ4Compression supported: 1
2025/07/08-01:11:46.433613 6103265280 	kBZip2Compression supported: 1
2025/07/08-01:11:46.433619 6103265280 Fast CRC32 supported: Supported on Arm64
2025/07/08-01:11:46.433626 6103265280 DMutex implementation: pthread_mutex_t
2025/07/08-01:11:46.433633 6103265280 Jemalloc supported: 0
2025/07/08-01:11:46.434103 6103265280 [db/db_impl/db_impl_open.cc:312] Creating manifest 1 
2025/07/08-01:11:46.434875 6103265280 [db/version_set.cc:6064] Recovering from manifest file: ./data/storage/MANIFEST-000001
2025/07/08-01:11:46.435123 6103265280 [db/column_family.cc:629] --------------- Options for column family [default]:
2025/07/08-01:11:46.435125 6103265280               Options.comparator: leveldb.BytewiseComparator
2025/07/08-01:11:46.435126 6103265280           Options.merge_operator: None
2025/07/08-01:11:46.435127 6103265280        Options.compaction_filter: None
2025/07/08-01:11:46.435128 6103265280        Options.compaction_filter_factory: None
2025/07/08-01:11:46.435138 6103265280  Options.sst_partitioner_factory: None
2025/07/08-01:11:46.435139 6103265280         Options.memtable_factory: SkipListFactory
2025/07/08-01:11:46.435140 6103265280            Options.table_factory: BlockBasedTable
2025/07/08-01:11:46.435159 6103265280            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x121e0f950)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x121e0f9a8
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0x0
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/08-01:11:46.435160 6103265280        Options.write_buffer_size: 67108864
2025/07/08-01:11:46.435161 6103265280  Options.max_write_buffer_number: 2
2025/07/08-01:11:46.435163 6103265280          Options.compression: Snappy
2025/07/08-01:11:46.435164 6103265280                  Options.bottommost_compression: Disabled
2025/07/08-01:11:46.435165 6103265280       Options.prefix_extractor: nullptr
2025/07/08-01:11:46.435166 6103265280   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/08-01:11:46.435167 6103265280             Options.num_levels: 7
2025/07/08-01:11:46.435168 6103265280        Options.min_write_buffer_number_to_merge: 1
2025/07/08-01:11:46.435169 6103265280     Options.max_write_buffer_number_to_maintain: 0
2025/07/08-01:11:46.435170 6103265280     Options.max_write_buffer_size_to_maintain: 0
2025/07/08-01:11:46.435171 6103265280            Options.bottommost_compression_opts.window_bits: -14
2025/07/08-01:11:46.435172 6103265280                  Options.bottommost_compression_opts.level: 32767
2025/07/08-01:11:46.435173 6103265280               Options.bottommost_compression_opts.strategy: 0
2025/07/08-01:11:46.435174 6103265280         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/08-01:11:46.435175 6103265280         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/08-01:11:46.435176 6103265280         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/08-01:11:46.435177 6103265280                  Options.bottommost_compression_opts.enabled: false
2025/07/08-01:11:46.435178 6103265280         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/08-01:11:46.435179 6103265280         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/08-01:11:46.435180 6103265280            Options.compression_opts.window_bits: -14
2025/07/08-01:11:46.435181 6103265280                  Options.compression_opts.level: 32767
2025/07/08-01:11:46.435182 6103265280               Options.compression_opts.strategy: 0
2025/07/08-01:11:46.435182 6103265280         Options.compression_opts.max_dict_bytes: 0
2025/07/08-01:11:46.435183 6103265280         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/08-01:11:46.435184 6103265280         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/08-01:11:46.435185 6103265280         Options.compression_opts.parallel_threads: 1
2025/07/08-01:11:46.435186 6103265280                  Options.compression_opts.enabled: false
2025/07/08-01:11:46.435187 6103265280         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/08-01:11:46.435188 6103265280      Options.level0_file_num_compaction_trigger: 4
2025/07/08-01:11:46.435189 6103265280          Options.level0_slowdown_writes_trigger: 20
2025/07/08-01:11:46.435190 6103265280              Options.level0_stop_writes_trigger: 36
2025/07/08-01:11:46.435191 6103265280                   Options.target_file_size_base: 67108864
2025/07/08-01:11:46.435192 6103265280             Options.target_file_size_multiplier: 1
2025/07/08-01:11:46.435193 6103265280                Options.max_bytes_for_level_base: 268435456
2025/07/08-01:11:46.435194 6103265280 Options.level_compaction_dynamic_level_bytes: 1
2025/07/08-01:11:46.435195 6103265280          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/08-01:11:46.435205 6103265280 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/08-01:11:46.435206 6103265280 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/08-01:11:46.435207 6103265280 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/08-01:11:46.435208 6103265280 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/08-01:11:46.435209 6103265280 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/08-01:11:46.435210 6103265280 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/08-01:11:46.435211 6103265280 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/08-01:11:46.435212 6103265280       Options.max_sequential_skip_in_iterations: 8
2025/07/08-01:11:46.435213 6103265280                    Options.max_compaction_bytes: 1677721600
2025/07/08-01:11:46.435214 6103265280                        Options.arena_block_size: 1048576
2025/07/08-01:11:46.435214 6103265280   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/08-01:11:46.435215 6103265280   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/08-01:11:46.435216 6103265280                Options.disable_auto_compactions: 0
2025/07/08-01:11:46.435218 6103265280                        Options.compaction_style: kCompactionStyleLevel
2025/07/08-01:11:46.435219 6103265280                          Options.compaction_pri: kMinOverlappingRatio
2025/07/08-01:11:46.435220 6103265280 Options.compaction_options_universal.size_ratio: 1
2025/07/08-01:11:46.435221 6103265280 Options.compaction_options_universal.min_merge_width: 2
2025/07/08-01:11:46.435222 6103265280 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/08-01:11:46.435225 6103265280 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/08-01:11:46.435226 6103265280 Options.compaction_options_universal.compression_size_percent: -1
2025/07/08-01:11:46.435227 6103265280 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/08-01:11:46.435228 6103265280 Options.compaction_options_universal.max_read_amp: -1
2025/07/08-01:11:46.435229 6103265280 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/08-01:11:46.435230 6103265280 Options.compaction_options_fifo.allow_compaction: 0
2025/07/08-01:11:46.435232 6103265280                   Options.table_properties_collectors: 
2025/07/08-01:11:46.435233 6103265280                   Options.inplace_update_support: 0
2025/07/08-01:11:46.435234 6103265280                 Options.inplace_update_num_locks: 10000
2025/07/08-01:11:46.435234 6103265280               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/08-01:11:46.435235 6103265280               Options.memtable_whole_key_filtering: 0
2025/07/08-01:11:46.435236 6103265280   Options.memtable_huge_page_size: 0
2025/07/08-01:11:46.435237 6103265280                           Options.bloom_locality: 0
2025/07/08-01:11:46.435238 6103265280                    Options.max_successive_merges: 0
2025/07/08-01:11:46.435239 6103265280             Options.strict_max_successive_merges: 0
2025/07/08-01:11:46.435240 6103265280                Options.optimize_filters_for_hits: 0
2025/07/08-01:11:46.435241 6103265280                Options.paranoid_file_checks: 0
2025/07/08-01:11:46.435242 6103265280                Options.force_consistency_checks: 1
2025/07/08-01:11:46.435243 6103265280                Options.report_bg_io_stats: 0
2025/07/08-01:11:46.435244 6103265280                               Options.ttl: 2592000
2025/07/08-01:11:46.435245 6103265280          Options.periodic_compaction_seconds: 0
2025/07/08-01:11:46.435246 6103265280                        Options.default_temperature: kUnknown
2025/07/08-01:11:46.435247 6103265280  Options.preclude_last_level_data_seconds: 0
2025/07/08-01:11:46.435248 6103265280    Options.preserve_internal_time_seconds: 0
2025/07/08-01:11:46.435249 6103265280                       Options.enable_blob_files: false
2025/07/08-01:11:46.435250 6103265280                           Options.min_blob_size: 0
2025/07/08-01:11:46.435250 6103265280                          Options.blob_file_size: 268435456
2025/07/08-01:11:46.435251 6103265280                   Options.blob_compression_type: NoCompression
2025/07/08-01:11:46.435253 6103265280          Options.enable_blob_garbage_collection: false
2025/07/08-01:11:46.435253 6103265280      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/08-01:11:46.435254 6103265280 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/08-01:11:46.435255 6103265280          Options.blob_compaction_readahead_size: 0
2025/07/08-01:11:46.435256 6103265280                Options.blob_file_starting_level: 0
2025/07/08-01:11:46.435257 6103265280         Options.experimental_mempurge_threshold: 0.000000
2025/07/08-01:11:46.435258 6103265280            Options.memtable_max_range_deletions: 0
2025/07/08-01:11:46.435784 6103265280 [db/version_set.cc:6122] Recovered from manifest file:./data/storage/MANIFEST-000001 succeeded,manifest_file_number is 1, next_file_number is 3, last_sequence is 0, log_number is 0,prev_log_number is 0,max_column_family is 0,min_log_number_to_keep is 0
2025/07/08-01:11:46.435786 6103265280 [db/version_set.cc:6131] Column family [default] (ID 0), log number is 0
2025/07/08-01:11:46.435788 6103265280 [db/db_impl/db_impl_open.cc:686] DB ID: 7b23d884-86e1-4f8b-838f-106a0939ec5c
2025/07/08-01:11:46.436017 6103265280 [db/version_set.cc:5552] Creating manifest 5
2025/07/08-01:11:46.436486 6103265280 [db/column_family.cc:629] --------------- Options for column family [configs]:
2025/07/08-01:11:46.436488 6103265280               Options.comparator: leveldb.BytewiseComparator
2025/07/08-01:11:46.436489 6103265280           Options.merge_operator: None
2025/07/08-01:11:46.436493 6103265280        Options.compaction_filter: None
2025/07/08-01:11:46.436494 6103265280        Options.compaction_filter_factory: None
2025/07/08-01:11:46.436496 6103265280  Options.sst_partitioner_factory: None
2025/07/08-01:11:46.436497 6103265280         Options.memtable_factory: SkipListFactory
2025/07/08-01:11:46.436498 6103265280            Options.table_factory: BlockBasedTable
2025/07/08-01:11:46.436507 6103265280            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x121c0aa30)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x121c0aa88
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0x0
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/08-01:11:46.436509 6103265280        Options.write_buffer_size: 67108864
2025/07/08-01:11:46.436510 6103265280  Options.max_write_buffer_number: 2
2025/07/08-01:11:46.436511 6103265280          Options.compression: Snappy
2025/07/08-01:11:46.436512 6103265280                  Options.bottommost_compression: Disabled
2025/07/08-01:11:46.436513 6103265280       Options.prefix_extractor: nullptr
2025/07/08-01:11:46.436514 6103265280   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/08-01:11:46.436515 6103265280             Options.num_levels: 7
2025/07/08-01:11:46.436516 6103265280        Options.min_write_buffer_number_to_merge: 1
2025/07/08-01:11:46.436517 6103265280     Options.max_write_buffer_number_to_maintain: 0
2025/07/08-01:11:46.436518 6103265280     Options.max_write_buffer_size_to_maintain: 0
2025/07/08-01:11:46.436519 6103265280            Options.bottommost_compression_opts.window_bits: -14
2025/07/08-01:11:46.436520 6103265280                  Options.bottommost_compression_opts.level: 32767
2025/07/08-01:11:46.436522 6103265280               Options.bottommost_compression_opts.strategy: 0
2025/07/08-01:11:46.436523 6103265280         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/08-01:11:46.436524 6103265280         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/08-01:11:46.436525 6103265280         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/08-01:11:46.436526 6103265280                  Options.bottommost_compression_opts.enabled: false
2025/07/08-01:11:46.436527 6103265280         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/08-01:11:46.436528 6103265280         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/08-01:11:46.436529 6103265280            Options.compression_opts.window_bits: -14
2025/07/08-01:11:46.436530 6103265280                  Options.compression_opts.level: 32767
2025/07/08-01:11:46.436531 6103265280               Options.compression_opts.strategy: 0
2025/07/08-01:11:46.436532 6103265280         Options.compression_opts.max_dict_bytes: 0
2025/07/08-01:11:46.436533 6103265280         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/08-01:11:46.436534 6103265280         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/08-01:11:46.436535 6103265280         Options.compression_opts.parallel_threads: 1
2025/07/08-01:11:46.436536 6103265280                  Options.compression_opts.enabled: false
2025/07/08-01:11:46.436537 6103265280         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/08-01:11:46.436538 6103265280      Options.level0_file_num_compaction_trigger: 4
2025/07/08-01:11:46.436539 6103265280          Options.level0_slowdown_writes_trigger: 20
2025/07/08-01:11:46.436540 6103265280              Options.level0_stop_writes_trigger: 36
2025/07/08-01:11:46.436541 6103265280                   Options.target_file_size_base: 67108864
2025/07/08-01:11:46.436542 6103265280             Options.target_file_size_multiplier: 1
2025/07/08-01:11:46.436543 6103265280                Options.max_bytes_for_level_base: 268435456
2025/07/08-01:11:46.436544 6103265280 Options.level_compaction_dynamic_level_bytes: 1
2025/07/08-01:11:46.436545 6103265280          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/08-01:11:46.436546 6103265280 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/08-01:11:46.436547 6103265280 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/08-01:11:46.436548 6103265280 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/08-01:11:46.436549 6103265280 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/08-01:11:46.436550 6103265280 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/08-01:11:46.436551 6103265280 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/08-01:11:46.436552 6103265280 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/08-01:11:46.436553 6103265280       Options.max_sequential_skip_in_iterations: 8
2025/07/08-01:11:46.436554 6103265280                    Options.max_compaction_bytes: 1677721600
2025/07/08-01:11:46.436555 6103265280                        Options.arena_block_size: 1048576
2025/07/08-01:11:46.436556 6103265280   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/08-01:11:46.436557 6103265280   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/08-01:11:46.436558 6103265280                Options.disable_auto_compactions: 0
2025/07/08-01:11:46.436559 6103265280                        Options.compaction_style: kCompactionStyleLevel
2025/07/08-01:11:46.436561 6103265280                          Options.compaction_pri: kMinOverlappingRatio
2025/07/08-01:11:46.436562 6103265280 Options.compaction_options_universal.size_ratio: 1
2025/07/08-01:11:46.436563 6103265280 Options.compaction_options_universal.min_merge_width: 2
2025/07/08-01:11:46.436564 6103265280 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/08-01:11:46.436565 6103265280 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/08-01:11:46.436566 6103265280 Options.compaction_options_universal.compression_size_percent: -1
2025/07/08-01:11:46.436567 6103265280 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/08-01:11:46.436568 6103265280 Options.compaction_options_universal.max_read_amp: -1
2025/07/08-01:11:46.436569 6103265280 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/08-01:11:46.436570 6103265280 Options.compaction_options_fifo.allow_compaction: 0
2025/07/08-01:11:46.436577 6103265280                   Options.table_properties_collectors: 
2025/07/08-01:11:46.436578 6103265280                   Options.inplace_update_support: 0
2025/07/08-01:11:46.436579 6103265280                 Options.inplace_update_num_locks: 10000
2025/07/08-01:11:46.436580 6103265280               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/08-01:11:46.436581 6103265280               Options.memtable_whole_key_filtering: 0
2025/07/08-01:11:46.436582 6103265280   Options.memtable_huge_page_size: 0
2025/07/08-01:11:46.436583 6103265280                           Options.bloom_locality: 0
2025/07/08-01:11:46.436584 6103265280                    Options.max_successive_merges: 0
2025/07/08-01:11:46.436585 6103265280             Options.strict_max_successive_merges: 0
2025/07/08-01:11:46.436586 6103265280                Options.optimize_filters_for_hits: 0
2025/07/08-01:11:46.436587 6103265280                Options.paranoid_file_checks: 0
2025/07/08-01:11:46.436588 6103265280                Options.force_consistency_checks: 1
2025/07/08-01:11:46.436589 6103265280                Options.report_bg_io_stats: 0
2025/07/08-01:11:46.436590 6103265280                               Options.ttl: 2592000
2025/07/08-01:11:46.436591 6103265280          Options.periodic_compaction_seconds: 0
2025/07/08-01:11:46.436592 6103265280                        Options.default_temperature: kUnknown
2025/07/08-01:11:46.436593 6103265280  Options.preclude_last_level_data_seconds: 0
2025/07/08-01:11:46.436594 6103265280    Options.preserve_internal_time_seconds: 0
2025/07/08-01:11:46.436595 6103265280                       Options.enable_blob_files: false
2025/07/08-01:11:46.436596 6103265280                           Options.min_blob_size: 0
2025/07/08-01:11:46.436597 6103265280                          Options.blob_file_size: 268435456
2025/07/08-01:11:46.436598 6103265280                   Options.blob_compression_type: NoCompression
2025/07/08-01:11:46.436599 6103265280          Options.enable_blob_garbage_collection: false
2025/07/08-01:11:46.436600 6103265280      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/08-01:11:46.436601 6103265280 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/08-01:11:46.436602 6103265280          Options.blob_compaction_readahead_size: 0
2025/07/08-01:11:46.436603 6103265280                Options.blob_file_starting_level: 0
2025/07/08-01:11:46.436604 6103265280         Options.experimental_mempurge_threshold: 0.000000
2025/07/08-01:11:46.436605 6103265280            Options.memtable_max_range_deletions: 0
2025/07/08-01:11:46.436682 6103265280 [db/db_impl/db_impl.cc:3673] Created column family [configs] (ID 1)
2025/07/08-01:11:46.436758 6103265280 [db/column_family.cc:629] --------------- Options for column family [versions]:
2025/07/08-01:11:46.436760 6103265280               Options.comparator: leveldb.BytewiseComparator
2025/07/08-01:11:46.436761 6103265280           Options.merge_operator: None
2025/07/08-01:11:46.436762 6103265280        Options.compaction_filter: None
2025/07/08-01:11:46.436763 6103265280        Options.compaction_filter_factory: None
2025/07/08-01:11:46.436764 6103265280  Options.sst_partitioner_factory: None
2025/07/08-01:11:46.436765 6103265280         Options.memtable_factory: SkipListFactory
2025/07/08-01:11:46.436766 6103265280            Options.table_factory: BlockBasedTable
2025/07/08-01:11:46.436773 6103265280            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x121c0d180)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x121c0d1d8
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0x0
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/08-01:11:46.436775 6103265280        Options.write_buffer_size: 67108864
2025/07/08-01:11:46.436775 6103265280  Options.max_write_buffer_number: 2
2025/07/08-01:11:46.436777 6103265280          Options.compression: Snappy
2025/07/08-01:11:46.436778 6103265280                  Options.bottommost_compression: Disabled
2025/07/08-01:11:46.436779 6103265280       Options.prefix_extractor: nullptr
2025/07/08-01:11:46.436780 6103265280   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/08-01:11:46.436781 6103265280             Options.num_levels: 7
2025/07/08-01:11:46.436782 6103265280        Options.min_write_buffer_number_to_merge: 1
2025/07/08-01:11:46.436783 6103265280     Options.max_write_buffer_number_to_maintain: 0
2025/07/08-01:11:46.436784 6103265280     Options.max_write_buffer_size_to_maintain: 0
2025/07/08-01:11:46.436785 6103265280            Options.bottommost_compression_opts.window_bits: -14
2025/07/08-01:11:46.436786 6103265280                  Options.bottommost_compression_opts.level: 32767
2025/07/08-01:11:46.436787 6103265280               Options.bottommost_compression_opts.strategy: 0
2025/07/08-01:11:46.436788 6103265280         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/08-01:11:46.436789 6103265280         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/08-01:11:46.436790 6103265280         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/08-01:11:46.436791 6103265280                  Options.bottommost_compression_opts.enabled: false
2025/07/08-01:11:46.436792 6103265280         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/08-01:11:46.436793 6103265280         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/08-01:11:46.436794 6103265280            Options.compression_opts.window_bits: -14
2025/07/08-01:11:46.436795 6103265280                  Options.compression_opts.level: 32767
2025/07/08-01:11:46.436796 6103265280               Options.compression_opts.strategy: 0
2025/07/08-01:11:46.436797 6103265280         Options.compression_opts.max_dict_bytes: 0
2025/07/08-01:11:46.436798 6103265280         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/08-01:11:46.436798 6103265280         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/08-01:11:46.436799 6103265280         Options.compression_opts.parallel_threads: 1
2025/07/08-01:11:46.436800 6103265280                  Options.compression_opts.enabled: false
2025/07/08-01:11:46.436801 6103265280         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/08-01:11:46.436802 6103265280      Options.level0_file_num_compaction_trigger: 4
2025/07/08-01:11:46.436803 6103265280          Options.level0_slowdown_writes_trigger: 20
2025/07/08-01:11:46.436804 6103265280              Options.level0_stop_writes_trigger: 36
2025/07/08-01:11:46.436805 6103265280                   Options.target_file_size_base: 67108864
2025/07/08-01:11:46.436806 6103265280             Options.target_file_size_multiplier: 1
2025/07/08-01:11:46.436807 6103265280                Options.max_bytes_for_level_base: 268435456
2025/07/08-01:11:46.436808 6103265280 Options.level_compaction_dynamic_level_bytes: 1
2025/07/08-01:11:46.436809 6103265280          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/08-01:11:46.436810 6103265280 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/08-01:11:46.436811 6103265280 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/08-01:11:46.436812 6103265280 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/08-01:11:46.436813 6103265280 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/08-01:11:46.436814 6103265280 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/08-01:11:46.436815 6103265280 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/08-01:11:46.436816 6103265280 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/08-01:11:46.436817 6103265280       Options.max_sequential_skip_in_iterations: 8
2025/07/08-01:11:46.436818 6103265280                    Options.max_compaction_bytes: 1677721600
2025/07/08-01:11:46.436819 6103265280                        Options.arena_block_size: 1048576
2025/07/08-01:11:46.436820 6103265280   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/08-01:11:46.436821 6103265280   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/08-01:11:46.436822 6103265280                Options.disable_auto_compactions: 0
2025/07/08-01:11:46.436823 6103265280                        Options.compaction_style: kCompactionStyleLevel
2025/07/08-01:11:46.436824 6103265280                          Options.compaction_pri: kMinOverlappingRatio
2025/07/08-01:11:46.436825 6103265280 Options.compaction_options_universal.size_ratio: 1
2025/07/08-01:11:46.436826 6103265280 Options.compaction_options_universal.min_merge_width: 2
2025/07/08-01:11:46.436827 6103265280 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/08-01:11:46.436828 6103265280 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/08-01:11:46.436829 6103265280 Options.compaction_options_universal.compression_size_percent: -1
2025/07/08-01:11:46.436830 6103265280 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/08-01:11:46.436831 6103265280 Options.compaction_options_universal.max_read_amp: -1
2025/07/08-01:11:46.436832 6103265280 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/08-01:11:46.436833 6103265280 Options.compaction_options_fifo.allow_compaction: 0
2025/07/08-01:11:46.436834 6103265280                   Options.table_properties_collectors: 
2025/07/08-01:11:46.436835 6103265280                   Options.inplace_update_support: 0
2025/07/08-01:11:46.436836 6103265280                 Options.inplace_update_num_locks: 10000
2025/07/08-01:11:46.436837 6103265280               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/08-01:11:46.436838 6103265280               Options.memtable_whole_key_filtering: 0
2025/07/08-01:11:46.436839 6103265280   Options.memtable_huge_page_size: 0
2025/07/08-01:11:46.436840 6103265280                           Options.bloom_locality: 0
2025/07/08-01:11:46.436841 6103265280                    Options.max_successive_merges: 0
2025/07/08-01:11:46.436842 6103265280             Options.strict_max_successive_merges: 0
2025/07/08-01:11:46.436843 6103265280                Options.optimize_filters_for_hits: 0
2025/07/08-01:11:46.436844 6103265280                Options.paranoid_file_checks: 0
2025/07/08-01:11:46.436845 6103265280                Options.force_consistency_checks: 1
2025/07/08-01:11:46.436846 6103265280                Options.report_bg_io_stats: 0
2025/07/08-01:11:46.436847 6103265280                               Options.ttl: 2592000
2025/07/08-01:11:46.436848 6103265280          Options.periodic_compaction_seconds: 0
2025/07/08-01:11:46.436849 6103265280                        Options.default_temperature: kUnknown
2025/07/08-01:11:46.436850 6103265280  Options.preclude_last_level_data_seconds: 0
2025/07/08-01:11:46.436851 6103265280    Options.preserve_internal_time_seconds: 0
2025/07/08-01:11:46.436852 6103265280                       Options.enable_blob_files: false
2025/07/08-01:11:46.436853 6103265280                           Options.min_blob_size: 0
2025/07/08-01:11:46.436854 6103265280                          Options.blob_file_size: 268435456
2025/07/08-01:11:46.436855 6103265280                   Options.blob_compression_type: NoCompression
2025/07/08-01:11:46.436856 6103265280          Options.enable_blob_garbage_collection: false
2025/07/08-01:11:46.436857 6103265280      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/08-01:11:46.436858 6103265280 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/08-01:11:46.436859 6103265280          Options.blob_compaction_readahead_size: 0
2025/07/08-01:11:46.436860 6103265280                Options.blob_file_starting_level: 0
2025/07/08-01:11:46.436861 6103265280         Options.experimental_mempurge_threshold: 0.000000
2025/07/08-01:11:46.436862 6103265280            Options.memtable_max_range_deletions: 0
2025/07/08-01:11:46.436898 6103265280 [db/db_impl/db_impl.cc:3673] Created column family [versions] (ID 2)
2025/07/08-01:11:46.436968 6103265280 [db/column_family.cc:629] --------------- Options for column family [logs]:
2025/07/08-01:11:46.436970 6103265280               Options.comparator: leveldb.BytewiseComparator
2025/07/08-01:11:46.436971 6103265280           Options.merge_operator: None
2025/07/08-01:11:46.436972 6103265280        Options.compaction_filter: None
2025/07/08-01:11:46.436973 6103265280        Options.compaction_filter_factory: None
2025/07/08-01:11:46.436974 6103265280  Options.sst_partitioner_factory: None
2025/07/08-01:11:46.436975 6103265280         Options.memtable_factory: SkipListFactory
2025/07/08-01:11:46.436976 6103265280            Options.table_factory: BlockBasedTable
2025/07/08-01:11:46.436982 6103265280            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x121c0f8d0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x121c0f928
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0x0
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/08-01:11:46.436986 6103265280        Options.write_buffer_size: 67108864
2025/07/08-01:11:46.436987 6103265280  Options.max_write_buffer_number: 2
2025/07/08-01:11:46.436988 6103265280          Options.compression: Snappy
2025/07/08-01:11:46.436989 6103265280                  Options.bottommost_compression: Disabled
2025/07/08-01:11:46.436990 6103265280       Options.prefix_extractor: nullptr
2025/07/08-01:11:46.436991 6103265280   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/08-01:11:46.436992 6103265280             Options.num_levels: 7
2025/07/08-01:11:46.436993 6103265280        Options.min_write_buffer_number_to_merge: 1
2025/07/08-01:11:46.436994 6103265280     Options.max_write_buffer_number_to_maintain: 0
2025/07/08-01:11:46.436995 6103265280     Options.max_write_buffer_size_to_maintain: 0
2025/07/08-01:11:46.436996 6103265280            Options.bottommost_compression_opts.window_bits: -14
2025/07/08-01:11:46.436997 6103265280                  Options.bottommost_compression_opts.level: 32767
2025/07/08-01:11:46.436998 6103265280               Options.bottommost_compression_opts.strategy: 0
2025/07/08-01:11:46.436999 6103265280         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/08-01:11:46.436999 6103265280         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/08-01:11:46.437000 6103265280         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/08-01:11:46.437001 6103265280                  Options.bottommost_compression_opts.enabled: false
2025/07/08-01:11:46.437002 6103265280         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/08-01:11:46.437003 6103265280         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/08-01:11:46.437004 6103265280            Options.compression_opts.window_bits: -14
2025/07/08-01:11:46.437005 6103265280                  Options.compression_opts.level: 32767
2025/07/08-01:11:46.437006 6103265280               Options.compression_opts.strategy: 0
2025/07/08-01:11:46.437007 6103265280         Options.compression_opts.max_dict_bytes: 0
2025/07/08-01:11:46.437008 6103265280         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/08-01:11:46.437009 6103265280         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/08-01:11:46.437010 6103265280         Options.compression_opts.parallel_threads: 1
2025/07/08-01:11:46.437011 6103265280                  Options.compression_opts.enabled: false
2025/07/08-01:11:46.437012 6103265280         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/08-01:11:46.437013 6103265280      Options.level0_file_num_compaction_trigger: 4
2025/07/08-01:11:46.437014 6103265280          Options.level0_slowdown_writes_trigger: 20
2025/07/08-01:11:46.437015 6103265280              Options.level0_stop_writes_trigger: 36
2025/07/08-01:11:46.437016 6103265280                   Options.target_file_size_base: 67108864
2025/07/08-01:11:46.437017 6103265280             Options.target_file_size_multiplier: 1
2025/07/08-01:11:46.437018 6103265280                Options.max_bytes_for_level_base: 268435456
2025/07/08-01:11:46.437019 6103265280 Options.level_compaction_dynamic_level_bytes: 1
2025/07/08-01:11:46.437020 6103265280          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/08-01:11:46.437021 6103265280 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/08-01:11:46.437022 6103265280 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/08-01:11:46.437023 6103265280 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/08-01:11:46.437024 6103265280 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/08-01:11:46.437025 6103265280 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/08-01:11:46.437026 6103265280 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/08-01:11:46.437026 6103265280 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/08-01:11:46.437027 6103265280       Options.max_sequential_skip_in_iterations: 8
2025/07/08-01:11:46.437028 6103265280                    Options.max_compaction_bytes: 1677721600
2025/07/08-01:11:46.437029 6103265280                        Options.arena_block_size: 1048576
2025/07/08-01:11:46.437030 6103265280   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/08-01:11:46.437031 6103265280   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/08-01:11:46.437032 6103265280                Options.disable_auto_compactions: 0
2025/07/08-01:11:46.437033 6103265280                        Options.compaction_style: kCompactionStyleLevel
2025/07/08-01:11:46.437035 6103265280                          Options.compaction_pri: kMinOverlappingRatio
2025/07/08-01:11:46.437036 6103265280 Options.compaction_options_universal.size_ratio: 1
2025/07/08-01:11:46.437037 6103265280 Options.compaction_options_universal.min_merge_width: 2
2025/07/08-01:11:46.437038 6103265280 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/08-01:11:46.437039 6103265280 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/08-01:11:46.437040 6103265280 Options.compaction_options_universal.compression_size_percent: -1
2025/07/08-01:11:46.437041 6103265280 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/08-01:11:46.437042 6103265280 Options.compaction_options_universal.max_read_amp: -1
2025/07/08-01:11:46.437043 6103265280 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/08-01:11:46.437044 6103265280 Options.compaction_options_fifo.allow_compaction: 0
2025/07/08-01:11:46.437045 6103265280                   Options.table_properties_collectors: 
2025/07/08-01:11:46.437046 6103265280                   Options.inplace_update_support: 0
2025/07/08-01:11:46.437047 6103265280                 Options.inplace_update_num_locks: 10000
2025/07/08-01:11:46.437048 6103265280               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/08-01:11:46.437049 6103265280               Options.memtable_whole_key_filtering: 0
2025/07/08-01:11:46.437050 6103265280   Options.memtable_huge_page_size: 0
2025/07/08-01:11:46.437051 6103265280                           Options.bloom_locality: 0
2025/07/08-01:11:46.437052 6103265280                    Options.max_successive_merges: 0
2025/07/08-01:11:46.437053 6103265280             Options.strict_max_successive_merges: 0
2025/07/08-01:11:46.437054 6103265280                Options.optimize_filters_for_hits: 0
2025/07/08-01:11:46.437055 6103265280                Options.paranoid_file_checks: 0
2025/07/08-01:11:46.437055 6103265280                Options.force_consistency_checks: 1
2025/07/08-01:11:46.437056 6103265280                Options.report_bg_io_stats: 0
2025/07/08-01:11:46.437057 6103265280                               Options.ttl: 2592000
2025/07/08-01:11:46.437058 6103265280          Options.periodic_compaction_seconds: 0
2025/07/08-01:11:46.437060 6103265280                        Options.default_temperature: kUnknown
2025/07/08-01:11:46.437061 6103265280  Options.preclude_last_level_data_seconds: 0
2025/07/08-01:11:46.437061 6103265280    Options.preserve_internal_time_seconds: 0
2025/07/08-01:11:46.437062 6103265280                       Options.enable_blob_files: false
2025/07/08-01:11:46.437063 6103265280                           Options.min_blob_size: 0
2025/07/08-01:11:46.437064 6103265280                          Options.blob_file_size: 268435456
2025/07/08-01:11:46.437065 6103265280                   Options.blob_compression_type: NoCompression
2025/07/08-01:11:46.437066 6103265280          Options.enable_blob_garbage_collection: false
2025/07/08-01:11:46.437067 6103265280      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/08-01:11:46.437068 6103265280 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/08-01:11:46.437069 6103265280          Options.blob_compaction_readahead_size: 0
2025/07/08-01:11:46.437070 6103265280                Options.blob_file_starting_level: 0
2025/07/08-01:11:46.437071 6103265280         Options.experimental_mempurge_threshold: 0.000000
2025/07/08-01:11:46.437072 6103265280            Options.memtable_max_range_deletions: 0
2025/07/08-01:11:46.437103 6103265280 [db/db_impl/db_impl.cc:3673] Created column family [logs] (ID 3)
2025/07/08-01:11:46.437167 6103265280 [db/column_family.cc:629] --------------- Options for column family [meta]:
2025/07/08-01:11:46.437168 6103265280               Options.comparator: leveldb.BytewiseComparator
2025/07/08-01:11:46.437170 6103265280           Options.merge_operator: None
2025/07/08-01:11:46.437171 6103265280        Options.compaction_filter: None
2025/07/08-01:11:46.437172 6103265280        Options.compaction_filter_factory: None
2025/07/08-01:11:46.437173 6103265280  Options.sst_partitioner_factory: None
2025/07/08-01:11:46.437174 6103265280         Options.memtable_factory: SkipListFactory
2025/07/08-01:11:46.437175 6103265280            Options.table_factory: BlockBasedTable
2025/07/08-01:11:46.437181 6103265280            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x121c12020)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x121c12078
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0x0
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/08-01:11:46.437182 6103265280        Options.write_buffer_size: 67108864
2025/07/08-01:11:46.437183 6103265280  Options.max_write_buffer_number: 2
2025/07/08-01:11:46.437184 6103265280          Options.compression: Snappy
2025/07/08-01:11:46.437185 6103265280                  Options.bottommost_compression: Disabled
2025/07/08-01:11:46.437186 6103265280       Options.prefix_extractor: nullptr
2025/07/08-01:11:46.437187 6103265280   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/08-01:11:46.437188 6103265280             Options.num_levels: 7
2025/07/08-01:11:46.437189 6103265280        Options.min_write_buffer_number_to_merge: 1
2025/07/08-01:11:46.437190 6103265280     Options.max_write_buffer_number_to_maintain: 0
2025/07/08-01:11:46.437191 6103265280     Options.max_write_buffer_size_to_maintain: 0
2025/07/08-01:11:46.437192 6103265280            Options.bottommost_compression_opts.window_bits: -14
2025/07/08-01:11:46.437193 6103265280                  Options.bottommost_compression_opts.level: 32767
2025/07/08-01:11:46.437194 6103265280               Options.bottommost_compression_opts.strategy: 0
2025/07/08-01:11:46.437195 6103265280         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/08-01:11:46.437196 6103265280         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/08-01:11:46.437197 6103265280         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/08-01:11:46.437198 6103265280                  Options.bottommost_compression_opts.enabled: false
2025/07/08-01:11:46.437198 6103265280         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/08-01:11:46.437199 6103265280         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/08-01:11:46.437200 6103265280            Options.compression_opts.window_bits: -14
2025/07/08-01:11:46.437201 6103265280                  Options.compression_opts.level: 32767
2025/07/08-01:11:46.437202 6103265280               Options.compression_opts.strategy: 0
2025/07/08-01:11:46.437203 6103265280         Options.compression_opts.max_dict_bytes: 0
2025/07/08-01:11:46.437204 6103265280         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/08-01:11:46.437205 6103265280         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/08-01:11:46.437206 6103265280         Options.compression_opts.parallel_threads: 1
2025/07/08-01:11:46.437207 6103265280                  Options.compression_opts.enabled: false
2025/07/08-01:11:46.437208 6103265280         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/08-01:11:46.437209 6103265280      Options.level0_file_num_compaction_trigger: 4
2025/07/08-01:11:46.437210 6103265280          Options.level0_slowdown_writes_trigger: 20
2025/07/08-01:11:46.437211 6103265280              Options.level0_stop_writes_trigger: 36
2025/07/08-01:11:46.437212 6103265280                   Options.target_file_size_base: 67108864
2025/07/08-01:11:46.437213 6103265280             Options.target_file_size_multiplier: 1
2025/07/08-01:11:46.437213 6103265280                Options.max_bytes_for_level_base: 268435456
2025/07/08-01:11:46.437214 6103265280 Options.level_compaction_dynamic_level_bytes: 1
2025/07/08-01:11:46.437215 6103265280          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/08-01:11:46.437216 6103265280 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/08-01:11:46.437217 6103265280 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/08-01:11:46.437218 6103265280 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/08-01:11:46.437219 6103265280 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/08-01:11:46.437220 6103265280 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/08-01:11:46.437221 6103265280 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/08-01:11:46.437222 6103265280 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/08-01:11:46.437223 6103265280       Options.max_sequential_skip_in_iterations: 8
2025/07/08-01:11:46.437224 6103265280                    Options.max_compaction_bytes: 1677721600
2025/07/08-01:11:46.437225 6103265280                        Options.arena_block_size: 1048576
2025/07/08-01:11:46.437226 6103265280   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/08-01:11:46.437227 6103265280   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/08-01:11:46.437228 6103265280                Options.disable_auto_compactions: 0
2025/07/08-01:11:46.437229 6103265280                        Options.compaction_style: kCompactionStyleLevel
2025/07/08-01:11:46.437230 6103265280                          Options.compaction_pri: kMinOverlappingRatio
2025/07/08-01:11:46.437231 6103265280 Options.compaction_options_universal.size_ratio: 1
2025/07/08-01:11:46.437232 6103265280 Options.compaction_options_universal.min_merge_width: 2
2025/07/08-01:11:46.437233 6103265280 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/08-01:11:46.437234 6103265280 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/08-01:11:46.437235 6103265280 Options.compaction_options_universal.compression_size_percent: -1
2025/07/08-01:11:46.437236 6103265280 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/08-01:11:46.437237 6103265280 Options.compaction_options_universal.max_read_amp: -1
2025/07/08-01:11:46.437238 6103265280 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/08-01:11:46.437239 6103265280 Options.compaction_options_fifo.allow_compaction: 0
2025/07/08-01:11:46.437240 6103265280                   Options.table_properties_collectors: 
2025/07/08-01:11:46.437241 6103265280                   Options.inplace_update_support: 0
2025/07/08-01:11:46.437242 6103265280                 Options.inplace_update_num_locks: 10000
2025/07/08-01:11:46.437243 6103265280               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/08-01:11:46.437244 6103265280               Options.memtable_whole_key_filtering: 0
2025/07/08-01:11:46.437245 6103265280   Options.memtable_huge_page_size: 0
2025/07/08-01:11:46.437246 6103265280                           Options.bloom_locality: 0
2025/07/08-01:11:46.437247 6103265280                    Options.max_successive_merges: 0
2025/07/08-01:11:46.437248 6103265280             Options.strict_max_successive_merges: 0
2025/07/08-01:11:46.437249 6103265280                Options.optimize_filters_for_hits: 0
2025/07/08-01:11:46.437249 6103265280                Options.paranoid_file_checks: 0
2025/07/08-01:11:46.437250 6103265280                Options.force_consistency_checks: 1
2025/07/08-01:11:46.437251 6103265280                Options.report_bg_io_stats: 0
2025/07/08-01:11:46.437252 6103265280                               Options.ttl: 2592000
2025/07/08-01:11:46.437253 6103265280          Options.periodic_compaction_seconds: 0
2025/07/08-01:11:46.437254 6103265280                        Options.default_temperature: kUnknown
2025/07/08-01:11:46.437255 6103265280  Options.preclude_last_level_data_seconds: 0
2025/07/08-01:11:46.437256 6103265280    Options.preserve_internal_time_seconds: 0
2025/07/08-01:11:46.437257 6103265280                       Options.enable_blob_files: false
2025/07/08-01:11:46.437258 6103265280                           Options.min_blob_size: 0
2025/07/08-01:11:46.437259 6103265280                          Options.blob_file_size: 268435456
2025/07/08-01:11:46.437260 6103265280                   Options.blob_compression_type: NoCompression
2025/07/08-01:11:46.437261 6103265280          Options.enable_blob_garbage_collection: false
2025/07/08-01:11:46.437262 6103265280      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/08-01:11:46.437263 6103265280 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/08-01:11:46.437264 6103265280          Options.blob_compaction_readahead_size: 0
2025/07/08-01:11:46.437265 6103265280                Options.blob_file_starting_level: 0
2025/07/08-01:11:46.437266 6103265280         Options.experimental_mempurge_threshold: 0.000000
2025/07/08-01:11:46.437267 6103265280            Options.memtable_max_range_deletions: 0
2025/07/08-01:11:46.437296 6103265280 [db/db_impl/db_impl.cc:3673] Created column family [meta] (ID 4)
2025/07/08-01:11:46.442193 6103265280 [db/db_impl/db_impl_open.cc:2252] SstFileManager instance 0x121b1f1a0
2025/07/08-01:11:46.442426 6103265280 DB pointer 0x120039000
2025/07/08-01:11:46.443301 6093107200 [db/db_impl/db_impl.cc:1210] ------- DUMPING STATS -------
2025/07/08-01:11:46.443307 6093107200 [db/db_impl/db_impl.cc:1211] 
** DB Stats **
Uptime(secs): 0.0 total, 0.0 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Estimated pending compaction bytes: 0
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x121e0f9a8#22689 capacity: 32.00 MB seed: 1947597287 usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 0.000106 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** Compaction Stats [configs] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [configs] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Estimated pending compaction bytes: 0
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x121c0aa88#22689 capacity: 32.00 MB seed: 1947597287 usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 9.7e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [configs] **

** Compaction Stats [versions] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [versions] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Estimated pending compaction bytes: 0
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x121c0d1d8#22689 capacity: 32.00 MB seed: 1947597287 usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 9.9e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [versions] **

** Compaction Stats [logs] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [logs] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Estimated pending compaction bytes: 0
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x121c0f928#22689 capacity: 32.00 MB seed: 1947597287 usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 9.8e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [logs] **

** Compaction Stats [meta] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [meta] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Estimated pending compaction bytes: 0
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x121c12078#22689 capacity: 32.00 MB seed: 1947597287 usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 9.7e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [meta] **
2025/07/08-01:11:46.443323 6103265280 [db/db_impl/db_impl.cc:500] Shutdown: canceling all background work
2025/07/08-01:11:46.443675 6103265280 [db/db_impl/db_impl.cc:709] Shutdown complete
